{"model_name": "Qwen2.5-Coder-1.5B-Instruct", "model_path": "models_llm/Qwen2.5-Coder-1.5B-Instruct-bnb-4bit", "use_qlora": true, "bits": 4, "quant_type": "nf4", "use_double_quant": true, "lora_r": 16, "lora_alpha": 32, "lora_dropout": 0.1, "target_modules": ["q_proj", "v_proj", "k_proj", "o_proj", "gate_proj", "up_proj", "down_proj"], "num_train_epochs": 4, "learning_rate": 0.0001, "min_learning_rate": 2e-05, "lr_scheduler_type": "cosine", "warmup_ratio": 0.1, "weight_decay": 0.05, "gradient_clipping": 0.2, "per_device_train_batch_size": 1, "gradient_accumulation_steps": 16, "effective_batch_size": 16, "optim": "adamw_torch", "adam_beta1": 0.9, "adam_beta2": 0.999, "adam_epsilon": 1e-08, "logging_steps": 10, "save_steps": 100, "eval_steps": 50, "save_total_limit": 3, "load_best_model_at_end": true, "metric_for_best_model": "eval_loss", "greater_is_better": false, "max_seq_length": 512, "dataset_text_field": "text", "packing": false, "evaluation_strategy": "steps", "eval_accumulation_steps": 1, "dataloader_num_workers": 4, "fp16": true, "gradient_checkpointing": true, "dataloader_pin_memory": true, "seed": 42, "data_seed": 42, "symbolic_validation": true, "fidelity_threshold": 0.95, "early_stopping_patience": 3, "benchmark_targets": {"logiqa": 0.6, "gsm8k": 0.5, "humaneval": 0.4}}