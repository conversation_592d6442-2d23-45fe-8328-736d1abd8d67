#!/usr/bin/env python3
"""
NEUROGLYPH LogiQA Benchmark Loader
Fase 5.2.2 - LogiQA Integration

Implementa:
- JSONL loader per dataset LogiQA
- Trasformazione domande in prompt testuali
- Parsing risposte multiple choice (A/B/C/D)
- Validazione logical reasoning
"""

import json
import re
from pathlib import Path
from typing import List, Any, Dict, Optional
from dataclasses import dataclass

from ..harness import BenchmarkLoader, EvaluationSample


@dataclass
class LogiQASample:
    """Sample LogiQA con metadati specifici."""
    
    id: str
    context: str
    question: str
    options: Dict[str, str]  # {"A": "option1", "B": "option2", ...}
    answer: str  # "A", "B", "C", or "D"
    category: Optional[str] = None
    difficulty: Optional[str] = None
    
    def to_evaluation_sample(self) -> EvaluationSample:
        """Converte a EvaluationSample standard."""
        return EvaluationSample(
            sample_id=self.id,
            prompt=self._format_prompt(),
            ground_truth=self.answer,
            metadata={
                'category': self.category,
                'difficulty': self.difficulty,
                'options': self.options,
                'context': self.context,
                'question': self.question
            }
        )
    
    def _format_prompt(self) -> str:
        """Formatta prompt per il modello."""
        prompt_parts = []
        
        # Context se presente
        if self.context and self.context.strip():
            prompt_parts.append(f"Context: {self.context.strip()}")
            prompt_parts.append("")
        
        # Question
        prompt_parts.append(f"Question: {self.question.strip()}")
        prompt_parts.append("")
        
        # Options
        prompt_parts.append("Options:")
        for key, value in sorted(self.options.items()):
            prompt_parts.append(f"{key}. {value}")
        
        return "\n".join(prompt_parts)


class LogiQALoader(BenchmarkLoader):
    """
    Loader per benchmark LogiQA.
    
    Supporta:
    - JSONL format standard
    - Multiple choice questions (A/B/C/D)
    - Context-based logical reasoning
    - Category e difficulty filtering
    """
    
    def __init__(self, prompt_template: Optional[str] = None):
        self.prompt_template = prompt_template or self._get_default_prompt_template()
        self.supported_formats = ['.jsonl', '.json']
        
    def _get_default_prompt_template(self) -> str:
        """Template di prompt di default per LogiQA."""
        return """You are an expert in logical reasoning. Analyze the given context and question carefully, then select the most logical answer.

{formatted_sample}

Instructions:
1. Read the context carefully if provided
2. Understand what the question is asking
3. Evaluate each option logically
4. Choose the option that follows most logically from the given information
5. Respond with only the letter (A, B, C, or D) of your chosen answer

Answer:"""
    
    def load_samples(self, data_path: str, max_samples: Optional[int] = None) -> List[EvaluationSample]:
        """
        Carica samples LogiQA da file JSONL.
        
        Args:
            data_path: Path al file JSONL LogiQA
            max_samples: Numero massimo di samples da caricare
            
        Returns:
            Lista di EvaluationSample
        """
        data_file = Path(data_path)

        # Se il path è "mock", usa samples mock per testing
        if data_path == "mock" or data_path.startswith("mock_"):
            return self._create_mock_samples(max_samples or 10)

        # Se il file non esiste, prova il dataset reale scaricato
        if not data_file.exists():
            real_dataset_path = Path("data/benchmarks/logiqa/test.jsonl")
            if real_dataset_path.exists():
                print(f"📊 Using real LogiQA dataset: {real_dataset_path}")
                data_file = real_dataset_path
            else:
                print(f"⚠️ Dataset not found: {data_path}, using mock samples")
                return self._create_mock_samples(max_samples or 10)
        
        if data_file.suffix not in self.supported_formats:
            raise ValueError(f"Formato file non supportato: {data_file.suffix}. Supportati: {self.supported_formats}")
        
        logiqa_samples = []
        
        try:
            if data_file.suffix == '.jsonl':
                logiqa_samples = self._load_jsonl(data_file)
            else:  # .json
                logiqa_samples = self._load_json(data_file)
                
        except Exception as e:
            print(f"⚠️ Errore caricamento {data_path}: {e}")
            print(f"🧪 Usando samples mock per testing")
            return self._create_mock_samples(max_samples or 10)
        
        # Limita numero di samples se richiesto
        if max_samples:
            logiqa_samples = logiqa_samples[:max_samples]
        
        # Converte a EvaluationSample
        evaluation_samples = [sample.to_evaluation_sample() for sample in logiqa_samples]
        
        print(f"📊 LogiQA: Caricati {len(evaluation_samples)} samples")
        if logiqa_samples:
            categories = set(s.category for s in logiqa_samples if s.category)
            if categories:
                print(f"📂 Categorie: {', '.join(sorted(categories))}")
        
        return evaluation_samples
    
    def _load_jsonl(self, file_path: Path) -> List[LogiQASample]:
        """Carica da file JSONL."""
        samples = []
        
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if not line:
                    continue
                
                try:
                    data = json.loads(line)
                    sample = self._parse_logiqa_data(data, f"line_{line_num}")
                    if sample:
                        samples.append(sample)
                except json.JSONDecodeError as e:
                    print(f"⚠️ Errore parsing linea {line_num}: {e}")
                except Exception as e:
                    print(f"⚠️ Errore sample linea {line_num}: {e}")
        
        return samples
    
    def _load_json(self, file_path: Path) -> List[LogiQASample]:
        """Carica da file JSON."""
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        samples = []
        
        # Supporta sia array di oggetti che oggetto con array
        if isinstance(data, list):
            items = data
        elif isinstance(data, dict) and 'data' in data:
            items = data['data']
        elif isinstance(data, dict) and 'samples' in data:
            items = data['samples']
        else:
            items = [data]  # Singolo oggetto
        
        for i, item in enumerate(items):
            try:
                sample = self._parse_logiqa_data(item, f"item_{i}")
                if sample:
                    samples.append(sample)
            except Exception as e:
                print(f"⚠️ Errore sample {i}: {e}")
        
        return samples
    
    def _parse_logiqa_data(self, data: Dict[str, Any], sample_id: str) -> Optional[LogiQASample]:
        """Parsa dati LogiQA da dizionario."""
        
        # Estrai campi richiesti
        try:
            # ID
            id_field = data.get('id', data.get('idx', sample_id))
            
            # Context (opzionale)
            context = data.get('context', data.get('passage', ''))
            
            # Question
            question = data.get('question', data.get('query', ''))
            if not question:
                return None
            
            # Options - supporta vari formati
            options = {}
            if 'options' in data:
                # Formato: {"options": ["opt1", "opt2", "opt3", "opt4"]}
                if isinstance(data['options'], list):
                    for i, opt in enumerate(data['options']):
                        options[chr(65 + i)] = opt  # A, B, C, D
                # Formato: {"options": {"A": "opt1", "B": "opt2", ...}}
                elif isinstance(data['options'], dict):
                    options = data['options']
            else:
                # Formato alternativo con campi separati
                for letter in ['A', 'B', 'C', 'D']:
                    if letter in data:
                        options[letter] = data[letter]
                    elif letter.lower() in data:
                        options[letter] = data[letter.lower()]
            
            if not options:
                return None
            
            # Answer
            answer = data.get('answer', data.get('label', ''))
            if not answer:
                return None
            
            # Normalizza answer
            if isinstance(answer, int):
                answer = chr(65 + answer)  # 0->A, 1->B, etc.
            answer = answer.upper().strip()
            
            if answer not in options:
                return None
            
            # Metadati opzionali
            category = data.get('category', data.get('type', None))
            difficulty = data.get('difficulty', data.get('level', None))
            
            return LogiQASample(
                id=str(id_field),
                context=context,
                question=question,
                options=options,
                answer=answer,
                category=category,
                difficulty=difficulty
            )
            
        except Exception as e:
            print(f"⚠️ Errore parsing sample {sample_id}: {e}")
            return None
    
    def _create_mock_samples(self, count: int) -> List[EvaluationSample]:
        """Crea samples mock per testing."""
        
        mock_samples = [
            LogiQASample(
                id="mock_1",
                context="All birds can fly. Penguins are birds.",
                question="Can penguins fly?",
                options={
                    "A": "Yes, because they are birds",
                    "B": "No, because they cannot fly despite being birds", 
                    "C": "Only in water",
                    "D": "Sometimes"
                },
                answer="B",
                category="logical_reasoning",
                difficulty="easy"
            ),
            LogiQASample(
                id="mock_2", 
                context="If it rains, the ground gets wet. The ground is wet.",
                question="What can we conclude?",
                options={
                    "A": "It rained",
                    "B": "It might have rained",
                    "C": "It did not rain",
                    "D": "We cannot determine if it rained"
                },
                answer="D",
                category="logical_reasoning", 
                difficulty="medium"
            ),
            LogiQASample(
                id="mock_3",
                context="All students who study hard pass the exam. John passed the exam.",
                question="What can we conclude about John?",
                options={
                    "A": "John studied hard",
                    "B": "John might have studied hard",
                    "C": "John did not study hard", 
                    "D": "We cannot determine if John studied hard"
                },
                answer="D",
                category="logical_reasoning",
                difficulty="hard"
            )
        ]
        
        # Replica samples se serve
        while len(mock_samples) < count:
            mock_samples.extend(mock_samples[:min(3, count - len(mock_samples))])
        
        return [sample.to_evaluation_sample() for sample in mock_samples[:count]]
    
    def format_prompt(self, sample: EvaluationSample) -> str:
        """
        Formatta prompt per il modello usando il template.
        
        Args:
            sample: Sample di evaluation
            
        Returns:
            Prompt formattato per il modello
        """
        # Estrai formatted sample dai metadati
        if 'context' in sample.metadata and 'question' in sample.metadata:
            # Ricostruisci formato LogiQA
            formatted_sample = sample.prompt
        else:
            # Usa prompt direttamente
            formatted_sample = sample.prompt
        
        return self.prompt_template.format(formatted_sample=formatted_sample)
    
    def parse_response(self, response: str, sample: EvaluationSample) -> Any:
        """
        Parsa risposta del modello per estrarre scelta multiple choice.
        
        Args:
            response: Risposta del modello
            sample: Sample originale
            
        Returns:
            Lettera della scelta (A, B, C, D) o None se non parsabile
        """
        if not response:
            return None
        
        response = response.strip()
        
        # Pattern per estrarre risposta
        patterns = [
            r'^([ABCD])$',  # Solo lettera
            r'^([ABCD])\.?$',  # Lettera con punto opzionale
            r'Answer:\s*([ABCD])',  # "Answer: A"
            r'answer:\s*([ABCD])',  # "answer: a" 
            r'([ABCD])\.',  # "A." all'inizio
            r'\b([ABCD])\b',  # Lettera isolata
        ]
        
        for pattern in patterns:
            match = re.search(pattern, response.upper())
            if match:
                letter = match.group(1).upper()
                # Verifica che sia una opzione valida
                if 'options' in sample.metadata and letter in sample.metadata['options']:
                    return letter
                elif letter in ['A', 'B', 'C', 'D']:  # Fallback
                    return letter
        
        # Fallback: cerca prima lettera A-D nel testo
        for char in response.upper():
            if char in ['A', 'B', 'C', 'D']:
                return char
        
        return None
    
    def evaluate_response(self, prediction: Any, ground_truth: Any) -> bool:
        """
        Valuta correttezza della risposta LogiQA.
        
        Args:
            prediction: Risposta predetta (A, B, C, D)
            ground_truth: Risposta corretta (A, B, C, D)
            
        Returns:
            True se corretta, False altrimenti
        """
        if prediction is None or ground_truth is None:
            return False
        
        # Normalizza entrambe le risposte
        pred_normalized = str(prediction).upper().strip()
        truth_normalized = str(ground_truth).upper().strip()
        
        return pred_normalized == truth_normalized
