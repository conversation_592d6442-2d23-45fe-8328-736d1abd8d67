"""
NEUROGLYPH Evaluation Benchmarks
Fase 5.2 - Reasoning Benchmarks Implementation

Benchmark loaders specifici per:
- LogiQA (logical reasoning)
- GSM8K (mathematical reasoning)
- HumanEval (code generation)
- ARC (commonsense reasoning)
- HellaSwag (reading comprehension)
"""

from .logiqa_loader import LogiQALoader

# Implementazione completa benchmark loaders
try:
    from .gsm8k_loader import GSM8K<PERSON>oader
    GSM8K_AVAILABLE = True
except ImportError:
    GSM8K_AVAILABLE = False

try:
    from .humaneval_loader import HumanEvalLoader
    HUMANEVAL_AVAILABLE = True
except ImportError:
    HUMANEVAL_AVAILABLE = False

__all__ = [
    'LogiQALoader',
    'GSM8KLoader',
    'HumanEvalLoader',
]

# Availability flags
BENCHMARK_AVAILABILITY = {
    'logiqa': True,
    'gsm8k': GSM8K_AVAILABLE,
    'humaneval': HUMANEVAL_AVAILABLE
}

# Aggiungi loaders disponibili dinamicamente
if GSM8K_AVAILABLE:
    __all__.append('GSM8KLoader')
if HUM<PERSON>EVAL_AVAILABLE:
    __all__.append('HumanEvalLoader')

__version__ = "5.2.3"
