#!/usr/bin/env python3
"""
NEUROGLYPH Fast Formula - PERFORMANCE MASSIME
Modulo formula semplificato senza infinite loops

PRINCIPI IMMUTABILI:
1. Atomicità: 1 simbolo = 1 token = 1 concetto
2. Unicità Unicode: nessun duplicato di codepoint
3. Reversibilità: AST ↔ NEUROGLYPH senza perdita
4. Semantica: mapping preciso a significato matematico/logico
5. Scientifico: riproducibilità + certificazione + audit trail
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Set, Union


class Term:
    """Termine logico base - PERFORMANCE MASSIME."""
    
    def __init__(self, name: str, term_type: str = "term"):
        self.name = name
        self.type = term_type
    
    def __str__(self) -> str:
        return self.name
    
    def __hash__(self) -> int:
        return hash((self.name, self.type))
    
    def __eq__(self, other) -> bool:
        return isinstance(other, Term) and self.name == other.name and self.type == other.type


class Variable(Term):
    """Variabile logica - PRINCIPIO 1: ATOMICA."""
    
    def __init__(self, name: str):
        # PRINCIPIO 1: Atomicità - variabile è atomica
        super().__init__(name.lower(), "variable")


class Constant(Term):
    """Costante logica - PRINCIPIO 1: ATOMICA."""
    
    def __init__(self, name: str):
        # PRINCIPIO 1: Atomicità - costante è atomica
        super().__init__(name.capitalize(), "constant")


class Formula(ABC):
    """Formula logica astratta - PRINCIPIO 4: SEMANTICA PRECISA."""
    
    @abstractmethod
    def __str__(self) -> str:
        """Rappresentazione stringa."""
        pass
    
    @abstractmethod
    def get_variables(self) -> Set[Variable]:
        """Ottiene variabili libere."""
        pass
    
    @abstractmethod
    def get_constants(self) -> Set[Constant]:
        """Ottiene costanti."""
        pass
    
    def __hash__(self) -> int:
        """Hash per set/dict."""
        return hash(str(self))
    
    def __eq__(self, other) -> bool:
        """Uguaglianza strutturale."""
        return isinstance(other, Formula) and str(self) == str(other)


class Predicate(Formula):
    """Predicato logico P(t1, t2, ...) - PRINCIPIO 4: SEMANTICA PRECISA."""
    
    def __init__(self, name: str, args: List[Term] = None):
        self.name = name
        self.args = args or []
    
    def __str__(self) -> str:
        if not self.args:
            return self.name
        args_str = ', '.join(str(arg) for arg in self.args)
        return f"{self.name}({args_str})"
    
    def get_variables(self) -> Set[Variable]:
        return {arg for arg in self.args if isinstance(arg, Variable)}
    
    def get_constants(self) -> Set[Constant]:
        return {arg for arg in self.args if isinstance(arg, Constant)}


class Conjunction(Formula):
    """Congiunzione A ∧ B - PRINCIPIO 2: SIMBOLO UNICO."""
    
    def __init__(self, left: Formula, right: Formula):
        self.left = left
        self.right = right
    
    def __str__(self) -> str:
        return f"({self.left} ∧ {self.right})"
    
    def get_variables(self) -> Set[Variable]:
        return self.left.get_variables() | self.right.get_variables()
    
    def get_constants(self) -> Set[Constant]:
        return self.left.get_constants() | self.right.get_constants()


class Disjunction(Formula):
    """Disgiunzione A ∨ B - PRINCIPIO 2: SIMBOLO UNICO."""
    
    def __init__(self, left: Formula, right: Formula):
        self.left = left
        self.right = right
    
    def __str__(self) -> str:
        return f"({self.left} ∨ {self.right})"
    
    def get_variables(self) -> Set[Variable]:
        return self.left.get_variables() | self.right.get_variables()
    
    def get_constants(self) -> Set[Constant]:
        return self.left.get_constants() | self.right.get_constants()


class Implication(Formula):
    """Implicazione A ⇒ B - PRINCIPIO 2: SIMBOLO UNICO."""
    
    def __init__(self, antecedent: Formula, consequent: Formula):
        self.antecedent = antecedent
        self.consequent = consequent
    
    def __str__(self) -> str:
        return f"({self.antecedent} ⇒ {self.consequent})"
    
    def get_variables(self) -> Set[Variable]:
        return self.antecedent.get_variables() | self.consequent.get_variables()
    
    def get_constants(self) -> Set[Constant]:
        return self.antecedent.get_constants() | self.consequent.get_constants()


class Negation(Formula):
    """Negazione ¬A - PRINCIPIO 2: SIMBOLO UNICO."""
    
    def __init__(self, formula: Formula):
        self.formula = formula
    
    def __str__(self) -> str:
        return f"¬{self.formula}"
    
    def get_variables(self) -> Set[Variable]:
        return self.formula.get_variables()
    
    def get_constants(self) -> Set[Constant]:
        return self.formula.get_constants()


class Universal(Formula):
    """Quantificatore universale ∀x P(x) - PRINCIPIO 2: SIMBOLO UNICO."""
    
    def __init__(self, variable: Variable, formula: Formula):
        self.variable = variable
        self.formula = formula
    
    def __str__(self) -> str:
        return f"∀{self.variable} {self.formula}"
    
    def get_variables(self) -> Set[Variable]:
        # Variabile quantificata non è libera
        free_vars = self.formula.get_variables()
        free_vars.discard(self.variable)
        return free_vars
    
    def get_constants(self) -> Set[Constant]:
        return self.formula.get_constants()


class Existential(Formula):
    """Quantificatore esistenziale ∃x P(x) - PRINCIPIO 2: SIMBOLO UNICO."""
    
    def __init__(self, variable: Variable, formula: Formula):
        self.variable = variable
        self.formula = formula
    
    def __str__(self) -> str:
        return f"∃{self.variable} {self.formula}"
    
    def get_variables(self) -> Set[Variable]:
        # Variabile quantificata non è libera
        free_vars = self.formula.get_variables()
        free_vars.discard(self.variable)
        return free_vars
    
    def get_constants(self) -> Set[Constant]:
        return self.formula.get_constants()


# Factory functions per costruzione rapida - PERFORMANCE MASSIME
def P(name: str, *args) -> Predicate:
    """Factory per predicati."""
    terms = []
    for arg in args:
        if isinstance(arg, str):
            if arg[0].islower():
                terms.append(Variable(arg))
            else:
                terms.append(Constant(arg))
        else:
            terms.append(arg)
    return Predicate(name, terms)


def And(left: Formula, right: Formula) -> Conjunction:
    """Factory per congiunzione."""
    return Conjunction(left, right)


def Or(left: Formula, right: Formula) -> Disjunction:
    """Factory per disgiunzione."""
    return Disjunction(left, right)


def Implies(antecedent: Formula, consequent: Formula) -> Implication:
    """Factory per implicazione."""
    return Implication(antecedent, consequent)


def Not(formula: Formula) -> Negation:
    """Factory per negazione."""
    return Negation(formula)


def ForAll(var: Union[str, Variable], formula: Formula) -> Universal:
    """Factory per quantificatore universale."""
    if isinstance(var, str):
        var = Variable(var)
    return Universal(var, formula)


def Exists(var: Union[str, Variable], formula: Formula) -> Existential:
    """Factory per quantificatore esistenziale."""
    if isinstance(var, str):
        var = Variable(var)
    return Existential(var, formula)
