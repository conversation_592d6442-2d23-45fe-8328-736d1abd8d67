#!/usr/bin/env python3
"""
NEUROGLYPH → Formula Converter
Converte simboli NEUROGLYPH in formule logiche per FormalLogicEngine

Principi Immutabili:
1. Atomicità: 1 simbolo = 1 token = 1 concetto
2. Unicità Unicode: nessun duplicato di codepoint
3. Reversibilità: AST ↔ NEUROGLYPH senza perdita
4. Semantica: mapping preciso a significato matematico/logico
5. Scientifico: riproducibilità + certificazione + audit trail
"""

import re
import json
import logging
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple, Any
from dataclasses import dataclass

from .formula import (
    Formula, Predicate, Variable, Constant, Term,
    Conjunction, Disjunction, Implication, Negation,
    Universal, Existential
)

logger = logging.getLogger(__name__)


@dataclass
class ConversionResult:
    """Risultato conversione NEUROGLYPH → Formula."""
    formula: Optional[Formula]
    success: bool
    confidence: float
    symbols_used: List[str]
    error_message: Optional[str] = None


class NeuroGlyphConverter:
    """
    Convertitore NEUROGLYPH → Formula Logica.
    
    Converte espressioni simboliche NEUROGLYPH in formule logiche
    utilizzabili dal FormalLogicEngine.
    """
    
    def __init__(self, registry_path: str = "data/registry/neuroglyph_symbols.json"):
        """
        Inizializza convertitore.
        
        Args:
            registry_path: Path al registry simboli NEUROGLYPH
        """
        self.registry_path = registry_path
        self.symbol_registry = {}
        self.logical_operators = {}
        self.conversion_stats = {
            'total_conversions': 0,
            'successful_conversions': 0,
            'failed_conversions': 0
        }
        
        self._load_symbol_registry()
        self._initialize_logical_operators()
        
        logger.info(f"🔄 NeuroGlyphConverter inizializzato con {len(self.symbol_registry)} simboli")
    
    def _load_symbol_registry(self):
        """Carica registry simboli NEUROGLYPH."""
        try:
            if Path(self.registry_path).exists():
                with open(self.registry_path, 'r', encoding='utf-8') as f:
                    registry_data = json.load(f)
                
                self.symbol_registry = registry_data
                logger.info(f"✅ Registry caricato: {len(registry_data.get('symbols', []))} simboli")
            else:
                logger.warning(f"⚠️ Registry non trovato: {self.registry_path}")
                self._create_fallback_registry()
        
        except Exception as e:
            logger.error(f"❌ Errore caricamento registry: {e}")
            self._create_fallback_registry()
    
    def _create_fallback_registry(self):
        """Crea registry fallback con simboli essenziali."""
        self.symbol_registry = {
            'symbols': ['∧', '∨', '⇒', '⇔', '¬', '∀', '∃', '⊢', '∴'],
            'categories': {
                'logical': ['∧', '∨', '⇒', '⇔', '¬', '∀', '∃', '⊢', '∴']
            }
        }
        logger.info("🔧 Registry fallback creato")
    
    def _initialize_logical_operators(self):
        """Inizializza mappatura operatori logici."""
        self.logical_operators = {
            # Connettivi logici
            '∧': 'and',
            '∨': 'or', 
            '⇒': 'implies',
            '→': 'implies',
            '⇔': 'iff',
            '↔': 'iff',
            '¬': 'not',
            
            # Quantificatori
            '∀': 'forall',
            '∃': 'exists',
            
            # Inferenza
            '⊢': 'entails',
            '∴': 'therefore',
            
            # Equivalenza
            '≡': 'equivalent',
            '≈': 'approximately',
            
            # Set theory
            '∈': 'in',
            '∉': 'not_in',
            '⊂': 'subset',
            '⊆': 'subset_eq',
            '∅': 'empty_set'
        }
        
        logger.debug(f"🔧 Operatori logici inizializzati: {len(self.logical_operators)}")
    
    def convert(self, neuroglyph_code: str) -> ConversionResult:
        """
        Converte codice NEUROGLYPH in Formula logica.
        
        Args:
            neuroglyph_code: Espressione simbolica NEUROGLYPH
            
        Returns:
            ConversionResult con formula convertita
        """
        self.conversion_stats['total_conversions'] += 1
        
        logger.debug(f"🔄 Converting: {neuroglyph_code}")
        
        try:
            # 1. Preprocessing
            cleaned_code = self._preprocess_code(neuroglyph_code)
            
            # 2. Tokenizzazione simbolica
            tokens = self._tokenize_symbols(cleaned_code)
            
            # 3. Parsing strutturale
            formula = self._parse_to_formula(tokens)
            
            if formula:
                self.conversion_stats['successful_conversions'] += 1
                
                # Calcola confidence basato su simboli riconosciuti
                symbols_used = self._extract_symbols(neuroglyph_code)
                confidence = self._calculate_confidence(symbols_used)
                
                logger.debug(f"✅ Conversion successful: {formula}")
                
                return ConversionResult(
                    formula=formula,
                    success=True,
                    confidence=confidence,
                    symbols_used=symbols_used
                )
            else:
                self.conversion_stats['failed_conversions'] += 1
                
                return ConversionResult(
                    formula=None,
                    success=False,
                    confidence=0.0,
                    symbols_used=[],
                    error_message="Failed to parse formula structure"
                )
        
        except Exception as e:
            self.conversion_stats['failed_conversions'] += 1
            
            logger.error(f"❌ Conversion error: {e}")
            
            return ConversionResult(
                formula=None,
                success=False,
                confidence=0.0,
                symbols_used=[],
                error_message=str(e)
            )
    
    def _preprocess_code(self, code: str) -> str:
        """Preprocessing del codice NEUROGLYPH."""
        # Rimuovi spazi extra
        cleaned = re.sub(r'\s+', ' ', code.strip())
        
        # Normalizza simboli comuni
        replacements = {
            '->': '⇒',
            '<->': '⇔',
            '&': '∧',
            '|': '∨',
            '~': '¬',
            'forall': '∀',
            'exists': '∃'
        }
        
        for old, new in replacements.items():
            cleaned = cleaned.replace(old, new)
        
        return cleaned
    
    def _tokenize_symbols(self, code: str) -> List[str]:
        """Tokenizza codice in simboli NEUROGLYPH."""
        # Pattern per simboli Unicode e identificatori
        pattern = r'[∀∃∧∨⇒⇔¬⊢∴≡≈∈∉⊂⊆∅]|[a-zA-Z_][a-zA-Z0-9_]*|\(|\)|,|\?'
        
        tokens = re.findall(pattern, code)
        
        # Filtra token vuoti
        tokens = [t for t in tokens if t.strip()]
        
        logger.debug(f"🔤 Tokens: {tokens}")
        return tokens
    
    def _parse_to_formula(self, tokens: List[str]) -> Optional[Formula]:
        """
        Parsa tokens in Formula logica.
        
        Args:
            tokens: Lista di token simbolici
            
        Returns:
            Formula logica o None se parsing fallisce
        """
        if not tokens:
            return None
        
        # Parser ricorsivo semplificato
        try:
            return self._parse_expression(tokens, 0)[0]
        except Exception as e:
            logger.debug(f"⚠️ Parse error: {e}")
            return None
    
    def _parse_expression(self, tokens: List[str], pos: int) -> Tuple[Optional[Formula], int]:
        """
        Parsa espressione logica ricorsivamente.
        
        Args:
            tokens: Lista token
            pos: Posizione corrente
            
        Returns:
            Tuple (Formula, nuova_posizione)
        """
        if pos >= len(tokens):
            return None, pos
        
        token = tokens[pos]
        
        # Quantificatori
        if token in ['∀', '∃']:
            return self._parse_quantifier(tokens, pos)
        
        # Negazione
        elif token == '¬':
            formula, new_pos = self._parse_expression(tokens, pos + 1)
            if formula:
                return Negation(formula), new_pos
            return None, pos + 1
        
        # Predicato o termine
        elif token.isalpha():
            return self._parse_predicate(tokens, pos)
        
        # Parentesi
        elif token == '(':
            return self._parse_parentheses(tokens, pos)
        
        else:
            # Token non riconosciuto, salta
            return None, pos + 1
    
    def _parse_quantifier(self, tokens: List[str], pos: int) -> Tuple[Optional[Formula], int]:
        """Parsa quantificatore (∀x P(x) o ∃x P(x))."""
        if pos + 2 >= len(tokens):
            return None, pos + 1
        
        quantifier = tokens[pos]
        variable_name = tokens[pos + 1]
        
        # Parsa formula quantificata
        formula, new_pos = self._parse_expression(tokens, pos + 2)
        
        if formula and variable_name.isalpha():
            variable = Variable(variable_name)
            
            if quantifier == '∀':
                return Universal(variable, formula), new_pos
            elif quantifier == '∃':
                return Existential(variable, formula), new_pos
        
        return None, pos + 1
    
    def _parse_predicate(self, tokens: List[str], pos: int) -> Tuple[Optional[Formula], int]:
        """Parsa predicato P(x,y) o costante."""
        if pos >= len(tokens):
            return None, pos
        
        name = tokens[pos]
        
        # Verifica se è seguito da parentesi (predicato con argomenti)
        if pos + 1 < len(tokens) and tokens[pos + 1] == '(':
            # Trova parentesi chiusa
            paren_count = 0
            end_pos = pos + 1
            
            for i in range(pos + 1, len(tokens)):
                if tokens[i] == '(':
                    paren_count += 1
                elif tokens[i] == ')':
                    paren_count -= 1
                    if paren_count == 0:
                        end_pos = i
                        break
            
            # Estrai argomenti
            args_tokens = tokens[pos + 2:end_pos]
            args = []
            
            for arg_token in args_tokens:
                if arg_token != ',' and arg_token.isalpha():
                    if arg_token[0].islower():
                        args.append(Variable(arg_token))
                    else:
                        args.append(Constant(arg_token))
            
            return Predicate(name, args), end_pos + 1
        
        else:
            # Predicato senza argomenti o costante
            return Predicate(name, []), pos + 1
    
    def _parse_parentheses(self, tokens: List[str], pos: int) -> Tuple[Optional[Formula], int]:
        """Parsa espressione tra parentesi."""
        if pos >= len(tokens) or tokens[pos] != '(':
            return None, pos
        
        # Trova parentesi chiusa
        paren_count = 0
        end_pos = pos
        
        for i in range(pos, len(tokens)):
            if tokens[i] == '(':
                paren_count += 1
            elif tokens[i] == ')':
                paren_count -= 1
                if paren_count == 0:
                    end_pos = i
                    break
        
        # Parsa contenuto tra parentesi
        inner_tokens = tokens[pos + 1:end_pos]
        formula, _ = self._parse_expression(inner_tokens, 0)
        
        return formula, end_pos + 1
    
    def _extract_symbols(self, code: str) -> List[str]:
        """Estrae simboli NEUROGLYPH dal codice."""
        symbols = []
        registry_symbols = self.symbol_registry.get('symbols', [])
        
        for symbol in registry_symbols:
            if symbol in code:
                symbols.append(symbol)
        
        return symbols
    
    def _calculate_confidence(self, symbols_used: List[str]) -> float:
        """Calcola confidence basato su simboli riconosciuti."""
        if not symbols_used:
            return 0.0
        
        registry_symbols = set(self.symbol_registry.get('symbols', []))
        recognized = sum(1 for s in symbols_used if s in registry_symbols)
        
        confidence = recognized / len(symbols_used) if symbols_used else 0.0
        return min(confidence, 1.0)
    
    def get_statistics(self) -> Dict[str, Any]:
        """Ottiene statistiche conversione."""
        total = self.conversion_stats['total_conversions']
        success_rate = (
            self.conversion_stats['successful_conversions'] / total 
            if total > 0 else 0.0
        )
        
        return {
            **self.conversion_stats,
            'success_rate': success_rate,
            'symbols_available': len(self.symbol_registry.get('symbols', [])),
            'operators_mapped': len(self.logical_operators)
        }


# Factory function per uso rapido
def convert_neuroglyph(neuroglyph_code: str) -> ConversionResult:
    """
    Converte codice NEUROGLYPH in Formula logica.
    
    Args:
        neuroglyph_code: Espressione simbolica NEUROGLYPH
        
    Returns:
        ConversionResult con formula convertita
    """
    converter = NeuroGlyphConverter()
    return converter.convert(neuroglyph_code)
