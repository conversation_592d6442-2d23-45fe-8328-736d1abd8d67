#!/usr/bin/env python3
"""
NEUROGLYPH → Formula Converter
Converte simboli NEUROGLYPH in formule logiche per FormalLogicEngine

Principi Immutabili:
1. Atomicità: 1 simbolo = 1 token = 1 concetto
2. Unicità Unicode: nessun duplicato di codepoint
3. Reversibilità: AST ↔ NEUROGLYPH senza perdita
4. Semantica: mapping preciso a significato matematico/logico
5. Scientifico: riproducibilità + certificazione + audit trail
"""

import re
import json
import logging
import hashlib
import time
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple, Any
from dataclasses import dataclass

from .formula import (
    Formula, Predicate, Variable, Constant, Term,
    Conjunction, Disjunction, Implication, Negation,
    Universal, Existential
)

logger = logging.getLogger(__name__)


@dataclass
class ConversionResult:
    """
    Risultato conversione NEUROGLYPH → Formula.

    PRINCIPI IMMUTABILI APPLICATI:
    - Atomicità: Ogni simbolo mappato atomicamente
    - Unicità: Verificata unicità Unicode
    - Reversibilità: Include reverse_formula per AST → NEUROGLYPH
    - Semantica: Mapping matematico preciso
    - Scientifico: Hash certificazione + audit trail
    """
    formula: Optional[Formula]
    success: bool
    confidence: float
    symbols_used: List[str]
    reverse_formula: Optional[str] = None  # PRINCIPIO 3: Reversibilità
    certification_hash: Optional[str] = None  # PRINCIPIO 5: Certificazione
    audit_trail: List[str] = None  # PRINCIPIO 5: Audit trail
    error_message: Optional[str] = None

    def __post_init__(self):
        if self.audit_trail is None:
            self.audit_trail = []


class NeuroGlyphConverter:
    """
    Convertitore NEUROGLYPH → Formula Logica.
    
    Converte espressioni simboliche NEUROGLYPH in formule logiche
    utilizzabili dal FormalLogicEngine.
    """
    
    def __init__(self, registry_path: str = "data/registry/neuroglyph_symbols.json"):
        """
        Inizializza convertitore.
        
        Args:
            registry_path: Path al registry simboli NEUROGLYPH
        """
        self.registry_path = registry_path
        self.symbol_registry = {}
        self.logical_operators = {}
        self.conversion_stats = {
            'total_conversions': 0,
            'successful_conversions': 0,
            'failed_conversions': 0
        }
        
        self._load_symbol_registry()
        self._initialize_logical_operators()
        
        logger.info(f"🔄 NeuroGlyphConverter inizializzato con {len(self.symbol_registry)} simboli")
    
    def _load_symbol_registry(self):
        """Carica registry simboli NEUROGLYPH."""
        try:
            if Path(self.registry_path).exists():
                with open(self.registry_path, 'r', encoding='utf-8') as f:
                    registry_data = json.load(f)
                
                self.symbol_registry = registry_data
                logger.info(f"✅ Registry caricato: {len(registry_data.get('symbols', []))} simboli")
            else:
                logger.warning(f"⚠️ Registry non trovato: {self.registry_path}")
                self._create_fallback_registry()
        
        except Exception as e:
            logger.error(f"❌ Errore caricamento registry: {e}")
            self._create_fallback_registry()
    
    def _create_fallback_registry(self):
        """Crea registry fallback con simboli essenziali."""
        self.symbol_registry = {
            'symbols': ['∧', '∨', '⇒', '⇔', '¬', '∀', '∃', '⊢', '∴'],
            'categories': {
                'logical': ['∧', '∨', '⇒', '⇔', '¬', '∀', '∃', '⊢', '∴']
            }
        }
        logger.info("🔧 Registry fallback creato")
    
    def _initialize_logical_operators(self):
        """
        Inizializza mappatura operatori logici.

        PRINCIPI IMMUTABILI APPLICATI:
        1. Atomicità: 1 simbolo = 1 concetto preciso
        2. Unicità: NO duplicati semantici
        4. Semantica: Mapping matematico/logico preciso
        """
        # PRINCIPIO 2: UNICITÀ UNICODE - UN SOLO SIMBOLO PER CONCETTO
        self.logical_operators = {
            # Connettivi logici - MAPPING PRECISO
            '∧': 'conjunction',      # AND logico
            '∨': 'disjunction',      # OR logico
            '⇒': 'implication',      # IMPLICAZIONE (NO duplicati con →)
            '⇔': 'biconditional',    # BICONDITIONAL (NO duplicati con ↔)
            '¬': 'negation',         # NEGAZIONE

            # Quantificatori - SEMANTICA PRECISA
            '∀': 'universal_quantifier',    # QUANTIFICATORE UNIVERSALE
            '∃': 'existential_quantifier',  # QUANTIFICATORE ESISTENZIALE

            # Inferenza logica - SIGNIFICATO MATEMATICO
            '⊢': 'logical_entailment',      # CONSEGUENZA LOGICA
            '∴': 'logical_conclusion',      # CONCLUSIONE

            # Equivalenza matematica - PRECISIONE SEMANTICA
            '≡': 'logical_equivalence',     # EQUIVALENZA LOGICA

            # Set theory - TEORIA DEGLI INSIEMI
            '∈': 'element_of',              # APPARTENENZA
            '∉': 'not_element_of',          # NON APPARTENENZA
            '⊂': 'proper_subset',           # SOTTOINSIEME PROPRIO
            '⊆': 'subset_or_equal',         # SOTTOINSIEME O UGUALE
            '∅': 'empty_set'                # INSIEME VUOTO
        }

        # PRINCIPIO 2: VERIFICA UNICITÀ
        self._verify_unicode_uniqueness()

        logger.info(f"🔧 Operatori logici inizializzati: {len(self.logical_operators)} (UNICITÀ VERIFICATA)")

    def _verify_unicode_uniqueness(self):
        """
        PRINCIPIO 2: Verifica unicità Unicode - nessun duplicato di codepoint.
        """
        symbols = list(self.logical_operators.keys())
        unique_symbols = set(symbols)

        if len(symbols) != len(unique_symbols):
            duplicates = [s for s in symbols if symbols.count(s) > 1]
            raise ValueError(f"VIOLAZIONE PRINCIPIO 2 - Duplicati Unicode: {duplicates}")

        # Verifica anche semantica unica
        semantics = list(self.logical_operators.values())
        unique_semantics = set(semantics)

        if len(semantics) != len(unique_semantics):
            duplicates = [s for s in semantics if semantics.count(s) > 1]
            raise ValueError(f"VIOLAZIONE PRINCIPIO 2 - Duplicati semantici: {duplicates}")

        logger.debug("✅ PRINCIPIO 2 VERIFICATO: Unicità Unicode garantita")

    def _generate_certification_hash(self, neuroglyph_code: str, formula: Formula) -> str:
        """
        PRINCIPIO 5: Genera hash certificazione per audit trail.
        """
        timestamp = str(int(time.time()))
        content = f"{neuroglyph_code}|{str(formula)}|{timestamp}"
        return hashlib.sha256(content.encode('utf-8')).hexdigest()[:16]

    def _create_reverse_formula(self, formula: Formula) -> str:
        """
        PRINCIPIO 3: Reversibilità - converte Formula → NEUROGLYPH.
        """
        try:
            return self._formula_to_neuroglyph(formula)
        except Exception as e:
            logger.warning(f"⚠️ Reversibilità parziale: {e}")
            return f"PARTIAL_REVERSE({str(formula)})"

    def _formula_to_neuroglyph(self, formula: Formula) -> str:
        """Converte Formula in simboli NEUROGLYPH (PRINCIPIO 3)."""
        if isinstance(formula, Predicate):
            if formula.args:
                args_str = ','.join(str(arg) for arg in formula.args)
                return f"{formula.name}({args_str})"
            else:
                return formula.name

        elif isinstance(formula, Conjunction):
            left = self._formula_to_neuroglyph(formula.left)
            right = self._formula_to_neuroglyph(formula.right)
            return f"({left} ∧ {right})"

        elif isinstance(formula, Disjunction):
            left = self._formula_to_neuroglyph(formula.left)
            right = self._formula_to_neuroglyph(formula.right)
            return f"({left} ∨ {right})"

        elif isinstance(formula, Implication):
            antecedent = self._formula_to_neuroglyph(formula.antecedent)
            consequent = self._formula_to_neuroglyph(formula.consequent)
            return f"({antecedent} ⇒ {consequent})"

        elif isinstance(formula, Negation):
            inner = self._formula_to_neuroglyph(formula.formula)
            return f"¬{inner}"

        elif isinstance(formula, Universal):
            var = str(formula.variable)
            inner = self._formula_to_neuroglyph(formula.formula)
            return f"∀{var} {inner}"

        elif isinstance(formula, Existential):
            var = str(formula.variable)
            inner = self._formula_to_neuroglyph(formula.formula)
            return f"∃{var} {inner}"

        else:
            return str(formula)
    
    def convert(self, neuroglyph_code: str) -> ConversionResult:
        """
        Converte codice NEUROGLYPH in Formula logica.

        APPLICA TUTTI I 5 PRINCIPI IMMUTABILI:
        1. Atomicità: Tokenizzazione atomica 1:1
        2. Unicità: Verifica unicità Unicode
        3. Reversibilità: Genera reverse formula
        4. Semantica: Mapping matematico preciso
        5. Scientifico: Certificazione + audit trail

        Args:
            neuroglyph_code: Espressione simbolica NEUROGLYPH

        Returns:
            ConversionResult con tutti i principi applicati
        """
        self.conversion_stats['total_conversions'] += 1
        audit_trail = [f"CONVERSION_START: {neuroglyph_code}"]

        logger.debug(f"🔄 Converting (PRINCIPI IMMUTABILI): {neuroglyph_code}")

        try:
            # PRINCIPIO 1: Preprocessing atomico
            cleaned_code = self._preprocess_code_atomic(neuroglyph_code)
            audit_trail.append(f"ATOMIC_PREPROCESSING: {cleaned_code}")

            # PRINCIPIO 1: Tokenizzazione atomica 1:1
            tokens = self._tokenize_atomic(cleaned_code)
            audit_trail.append(f"ATOMIC_TOKENIZATION: {len(tokens)} tokens")

            # PRINCIPIO 4: Parsing semantico preciso
            formula = self._parse_to_formula_semantic(tokens)
            audit_trail.append(f"SEMANTIC_PARSING: {formula is not None}")

            if formula:
                self.conversion_stats['successful_conversions'] += 1

                # PRINCIPIO 1: Estrazione simboli atomica
                symbols_used = self._extract_symbols_atomic(neuroglyph_code)

                # PRINCIPIO 4: Confidence semantico
                confidence = self._calculate_semantic_confidence(symbols_used)

                # PRINCIPIO 3: Reversibilità
                reverse_formula = self._create_reverse_formula(formula)
                audit_trail.append(f"REVERSIBILITY: {reverse_formula}")

                # PRINCIPIO 5: Certificazione scientifica
                cert_hash = self._generate_certification_hash(neuroglyph_code, formula)
                audit_trail.append(f"CERTIFICATION: {cert_hash}")

                # PRINCIPIO 3: Verifica reversibilità
                reversibility_check = self._verify_reversibility(neuroglyph_code, reverse_formula)
                audit_trail.append(f"REVERSIBILITY_CHECK: {reversibility_check}")

                logger.debug(f"✅ Conversion successful (ALL PRINCIPLES): {formula}")

                return ConversionResult(
                    formula=formula,
                    success=True,
                    confidence=confidence,
                    symbols_used=symbols_used,
                    reverse_formula=reverse_formula,
                    certification_hash=cert_hash,
                    audit_trail=audit_trail
                )
            else:
                self.conversion_stats['failed_conversions'] += 1
                audit_trail.append("PARSING_FAILED")

                return ConversionResult(
                    formula=None,
                    success=False,
                    confidence=0.0,
                    symbols_used=[],
                    audit_trail=audit_trail,
                    error_message="Failed to parse formula structure"
                )

        except Exception as e:
            self.conversion_stats['failed_conversions'] += 1
            audit_trail.append(f"ERROR: {str(e)}")

            logger.error(f"❌ Conversion error: {e}")

            return ConversionResult(
                formula=None,
                success=False,
                confidence=0.0,
                symbols_used=[],
                audit_trail=audit_trail,
                error_message=str(e)
            )
    
    def _preprocess_code_atomic(self, code: str) -> str:
        """
        PRINCIPIO 1: Preprocessing atomico del codice NEUROGLYPH.
        Ogni simbolo deve rimanere atomico e indivisibile.
        """
        # PRINCIPIO 1: Preserva atomicità - NO sostituzione che rompe simboli
        cleaned = re.sub(r'\s+', ' ', code.strip())

        # PRINCIPIO 2: SOLO normalizzazioni che preservano unicità
        # NO sostituzioni che creano duplicati semantici
        atomic_replacements = {
            # Solo ASCII → Unicode atomico, NO duplicati
            'forall': '∀',  # OK: ASCII → simbolo atomico
            'exists': '∃',  # OK: ASCII → simbolo atomico
        }

        for old, new in atomic_replacements.items():
            # PRINCIPIO 1: Sostituzione atomica preservando boundaries
            cleaned = re.sub(r'\b' + re.escape(old) + r'\b', new, cleaned)

        return cleaned

    def _tokenize_atomic(self, code: str) -> List[str]:
        """
        PRINCIPIO 1: Tokenizzazione atomica - 1 simbolo = 1 token = 1 concetto.
        PERFORMANCE MASSIME: Tokenizzazione diretta senza regex complessi.
        """
        # PRINCIPIO 1: Tokenizzazione atomica DIRETTA (no regex infiniti)
        tokens = []
        current_token = ""

        # PRINCIPIO 2: Simboli Unicode unici dal registry
        logical_symbols = {'∀', '∃', '∧', '∨', '⇒', '⇔', '¬', '⊢', '∴', '≡', '≈', '∈', '∉', '⊂', '⊆', '∅'}
        delimiters = {'(', ')', ':', ',', '?', ' '}

        i = 0
        while i < len(code):
            char = code[i]

            # PRINCIPIO 1: Simbolo atomico
            if char in logical_symbols:
                if current_token.strip():
                    tokens.append(current_token.strip())
                    current_token = ""
                tokens.append(char)

            # Delimitatori
            elif char in delimiters:
                if current_token.strip():
                    tokens.append(current_token.strip())
                    current_token = ""
                if char != ' ':  # Ignora spazi
                    tokens.append(char)

            # Caratteri alfanumerici
            else:
                current_token += char

            i += 1

        # Token finale
        if current_token.strip():
            tokens.append(current_token.strip())

        logger.debug(f"🔤 ATOMIC Tokens: {tokens}")
        return tokens

    def _parse_to_formula_semantic(self, tokens: List[str]) -> Optional[Formula]:
        """
        PRINCIPIO 4: Parsing semantico preciso - mapping matematico/logico esatto.
        """
        if not tokens:
            return None

        # PRINCIPIO 4: Parser semantico che rispetta significato matematico
        try:
            return self._parse_expression_semantic(tokens, 0)[0]
        except Exception as e:
            logger.debug(f"⚠️ Semantic parse error: {e}")
            return None

    def _parse_expression_semantic(self, tokens: List[str], pos: int) -> Tuple[Optional[Formula], int]:
        """
        PRINCIPIO 4: Parsing semantico ricorsivo con significato matematico preciso.
        """
        if pos >= len(tokens):
            return None, pos

        token = tokens[pos]

        # PRINCIPIO 4: Semantica precisa per quantificatori
        if token in ['∀', '∃']:
            return self._parse_quantifier_semantic(tokens, pos)

        # PRINCIPIO 4: Semantica precisa per negazione
        elif token == '¬':
            formula, new_pos = self._parse_expression_semantic(tokens, pos + 1)
            if formula:
                return Negation(formula), new_pos
            return None, pos + 1

        # PRINCIPIO 4: Semantica precisa per predicati
        elif token.isalpha():
            return self._parse_predicate_semantic(tokens, pos)

        # PRINCIPIO 4: Semantica precisa per parentesi
        elif token == '(':
            return self._parse_parentheses_semantic(tokens, pos)

        else:
            return None, pos + 1

    def _parse_quantifier_semantic(self, tokens: List[str], pos: int) -> Tuple[Optional[Formula], int]:
        """PRINCIPIO 4: Parsing semantico quantificatori."""
        if pos + 2 >= len(tokens):
            return None, pos + 1

        quantifier = tokens[pos]
        variable_name = tokens[pos + 1]

        formula, new_pos = self._parse_expression_semantic(tokens, pos + 2)

        if formula and variable_name.isalpha():
            variable = Variable(variable_name)

            if quantifier == '∀':
                return Universal(variable, formula), new_pos
            elif quantifier == '∃':
                return Existential(variable, formula), new_pos

        return None, pos + 1

    def _parse_predicate_semantic(self, tokens: List[str], pos: int) -> Tuple[Optional[Formula], int]:
        """PRINCIPIO 4: Parsing semantico predicati."""
        if pos >= len(tokens):
            return None, pos

        name = tokens[pos]

        if pos + 1 < len(tokens) and tokens[pos + 1] == '(':
            paren_count = 0
            end_pos = pos + 1

            for i in range(pos + 1, len(tokens)):
                if tokens[i] == '(':
                    paren_count += 1
                elif tokens[i] == ')':
                    paren_count -= 1
                    if paren_count == 0:
                        end_pos = i
                        break

            args_tokens = tokens[pos + 2:end_pos]
            args = []

            for arg_token in args_tokens:
                if arg_token != ',' and arg_token.isalpha():
                    if arg_token[0].islower():
                        args.append(Variable(arg_token))
                    else:
                        args.append(Constant(arg_token))

            return Predicate(name, args), end_pos + 1
        else:
            return Predicate(name, []), pos + 1

    def _parse_parentheses_semantic(self, tokens: List[str], pos: int) -> Tuple[Optional[Formula], int]:
        """PRINCIPIO 4: Parsing semantico parentesi."""
        if pos >= len(tokens) or tokens[pos] != '(':
            return None, pos

        paren_count = 0
        end_pos = pos

        for i in range(pos, len(tokens)):
            if tokens[i] == '(':
                paren_count += 1
            elif tokens[i] == ')':
                paren_count -= 1
                if paren_count == 0:
                    end_pos = i
                    break

        inner_tokens = tokens[pos + 1:end_pos]
        formula, _ = self._parse_expression_semantic(inner_tokens, 0)

        return formula, end_pos + 1

    def _extract_symbols_atomic(self, code: str) -> List[str]:
        """PRINCIPIO 1: Estrazione simboli atomica."""
        symbols = []
        registry_symbols = self.symbol_registry.get('symbols', [])

        for symbol in registry_symbols:
            if symbol in code:
                symbols.append(symbol)

        return symbols

    def _calculate_semantic_confidence(self, symbols_used: List[str]) -> float:
        """PRINCIPIO 4: Confidence basato su semantica precisa."""
        if not symbols_used:
            return 0.0

        registry_symbols = set(self.symbol_registry.get('symbols', []))
        logical_symbols = set(self.symbol_registry.get('categories', {}).get('logical', []))

        # Peso maggiore per simboli logici (semantica più precisa)
        logical_weight = 0.8
        general_weight = 0.2

        logical_recognized = sum(logical_weight for s in symbols_used if s in logical_symbols)
        general_recognized = sum(general_weight for s in symbols_used if s in registry_symbols and s not in logical_symbols)

        total_weight = len(symbols_used) * logical_weight  # Peso massimo possibile
        actual_weight = logical_recognized + general_recognized

        confidence = actual_weight / total_weight if total_weight > 0 else 0.0
        return min(confidence, 1.0)

    def _verify_reversibility(self, original: str, reverse: str) -> bool:
        """
        PRINCIPIO 3: Verifica reversibilità AST ↔ NEUROGLYPH.
        PRINCIPIO 5: SCIENTIFICO - No loop infiniti.
        """
        try:
            # PRINCIPIO 5: Verifica semplice senza ricorsione infinita
            # Controlla che reverse contenga simboli logici validi
            logical_symbols = {'∀', '∃', '∧', '∨', '⇒', '⇔', '¬', '⊢', '∴'}
            has_logical_symbols = any(symbol in reverse for symbol in logical_symbols)

            # PRINCIPIO 3: Verifica strutturale base
            has_structure = len(reverse.strip()) > 0 and reverse != original

            return has_logical_symbols or has_structure

        except Exception:
            return False
    
    def _tokenize_symbols(self, code: str) -> List[str]:
        """Tokenizza codice in simboli NEUROGLYPH."""
        # Pattern per simboli Unicode e identificatori
        pattern = r'[∀∃∧∨⇒⇔¬⊢∴≡≈∈∉⊂⊆∅]|[a-zA-Z_][a-zA-Z0-9_]*|\(|\)|,|\?'
        
        tokens = re.findall(pattern, code)
        
        # Filtra token vuoti
        tokens = [t for t in tokens if t.strip()]
        
        logger.debug(f"🔤 Tokens: {tokens}")
        return tokens
    
    def _parse_to_formula(self, tokens: List[str]) -> Optional[Formula]:
        """
        Parsa tokens in Formula logica.
        
        Args:
            tokens: Lista di token simbolici
            
        Returns:
            Formula logica o None se parsing fallisce
        """
        if not tokens:
            return None
        
        # Parser ricorsivo semplificato
        try:
            return self._parse_expression(tokens, 0)[0]
        except Exception as e:
            logger.debug(f"⚠️ Parse error: {e}")
            return None
    
    def _parse_expression(self, tokens: List[str], pos: int) -> Tuple[Optional[Formula], int]:
        """
        Parsa espressione logica ricorsivamente.
        
        Args:
            tokens: Lista token
            pos: Posizione corrente
            
        Returns:
            Tuple (Formula, nuova_posizione)
        """
        if pos >= len(tokens):
            return None, pos
        
        token = tokens[pos]
        
        # Quantificatori
        if token in ['∀', '∃']:
            return self._parse_quantifier(tokens, pos)
        
        # Negazione
        elif token == '¬':
            formula, new_pos = self._parse_expression(tokens, pos + 1)
            if formula:
                return Negation(formula), new_pos
            return None, pos + 1
        
        # Predicato o termine
        elif token.isalpha():
            return self._parse_predicate(tokens, pos)
        
        # Parentesi
        elif token == '(':
            return self._parse_parentheses(tokens, pos)
        
        else:
            # Token non riconosciuto, salta
            return None, pos + 1
    
    def _parse_quantifier(self, tokens: List[str], pos: int) -> Tuple[Optional[Formula], int]:
        """Parsa quantificatore (∀x P(x) o ∃x P(x))."""
        if pos + 2 >= len(tokens):
            return None, pos + 1
        
        quantifier = tokens[pos]
        variable_name = tokens[pos + 1]
        
        # Parsa formula quantificata
        formula, new_pos = self._parse_expression(tokens, pos + 2)
        
        if formula and variable_name.isalpha():
            variable = Variable(variable_name)
            
            if quantifier == '∀':
                return Universal(variable, formula), new_pos
            elif quantifier == '∃':
                return Existential(variable, formula), new_pos
        
        return None, pos + 1
    
    def _parse_predicate(self, tokens: List[str], pos: int) -> Tuple[Optional[Formula], int]:
        """Parsa predicato P(x,y) o costante."""
        if pos >= len(tokens):
            return None, pos
        
        name = tokens[pos]
        
        # Verifica se è seguito da parentesi (predicato con argomenti)
        if pos + 1 < len(tokens) and tokens[pos + 1] == '(':
            # Trova parentesi chiusa
            paren_count = 0
            end_pos = pos + 1
            
            for i in range(pos + 1, len(tokens)):
                if tokens[i] == '(':
                    paren_count += 1
                elif tokens[i] == ')':
                    paren_count -= 1
                    if paren_count == 0:
                        end_pos = i
                        break
            
            # Estrai argomenti
            args_tokens = tokens[pos + 2:end_pos]
            args = []
            
            for arg_token in args_tokens:
                if arg_token != ',' and arg_token.isalpha():
                    if arg_token[0].islower():
                        args.append(Variable(arg_token))
                    else:
                        args.append(Constant(arg_token))
            
            return Predicate(name, args), end_pos + 1
        
        else:
            # Predicato senza argomenti o costante
            return Predicate(name, []), pos + 1
    
    def _parse_parentheses(self, tokens: List[str], pos: int) -> Tuple[Optional[Formula], int]:
        """Parsa espressione tra parentesi."""
        if pos >= len(tokens) or tokens[pos] != '(':
            return None, pos
        
        # Trova parentesi chiusa
        paren_count = 0
        end_pos = pos
        
        for i in range(pos, len(tokens)):
            if tokens[i] == '(':
                paren_count += 1
            elif tokens[i] == ')':
                paren_count -= 1
                if paren_count == 0:
                    end_pos = i
                    break
        
        # Parsa contenuto tra parentesi
        inner_tokens = tokens[pos + 1:end_pos]
        formula, _ = self._parse_expression(inner_tokens, 0)
        
        return formula, end_pos + 1
    
    def _extract_symbols(self, code: str) -> List[str]:
        """Estrae simboli NEUROGLYPH dal codice."""
        symbols = []
        registry_symbols = self.symbol_registry.get('symbols', [])
        
        for symbol in registry_symbols:
            if symbol in code:
                symbols.append(symbol)
        
        return symbols
    
    def _calculate_confidence(self, symbols_used: List[str]) -> float:
        """Calcola confidence basato su simboli riconosciuti."""
        if not symbols_used:
            return 0.0
        
        registry_symbols = set(self.symbol_registry.get('symbols', []))
        recognized = sum(1 for s in symbols_used if s in registry_symbols)
        
        confidence = recognized / len(symbols_used) if symbols_used else 0.0
        return min(confidence, 1.0)
    
    def get_statistics(self) -> Dict[str, Any]:
        """Ottiene statistiche conversione."""
        total = self.conversion_stats['total_conversions']
        success_rate = (
            self.conversion_stats['successful_conversions'] / total 
            if total > 0 else 0.0
        )
        
        return {
            **self.conversion_stats,
            'success_rate': success_rate,
            'symbols_available': len(self.symbol_registry.get('symbols', [])),
            'operators_mapped': len(self.logical_operators)
        }


# Factory function per uso rapido
def convert_neuroglyph(neuroglyph_code: str) -> ConversionResult:
    """
    Converte codice NEUROGLYPH in Formula logica.
    
    Args:
        neuroglyph_code: Espressione simbolica NEUROGLYPH
        
    Returns:
        ConversionResult con formula convertita
    """
    converter = NeuroGlyphConverter()
    return converter.convert(neuroglyph_code)
