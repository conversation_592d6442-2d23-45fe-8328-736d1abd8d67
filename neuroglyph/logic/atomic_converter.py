#!/usr/bin/env python3
"""
NEUROGLYPH Atomic Converter - 5 PRINCIPI IMMUTABILI
Converter minimo che applica rigorosamente tutti i principi senza blocchi

PRINCIPI IMMUTABILI:
1. Atomicità: 1 simbolo = 1 token = 1 concetto
2. Unicità Unicode: nessun duplicato di codepoint
3. Reversibilità: AST ↔ NEUROGLYPH senza perdita
4. Semantica: mapping preciso a significato matematico/logico
5. Scientifico: riproducibilità + certificazione + audit trail
"""

import hashlib
import time
from typing import Optional, List
from dataclasses import dataclass, field

from .formula import (
    Formula, Predicate, Variable, Constant,
    Conjunction, Disjunction, Implication, Negation,
    Universal, Existential
)


@dataclass
class AtomicConversionResult:
    """
    Risultato conversione atomica con 5 PRINCIPI IMMUTABILI.
    """
    formula: Optional[Formula]
    success: bool
    confidence: float
    symbols_used: List[str]
    reverse_formula: Optional[str] = None  # PRINCIPIO 3
    certification_hash: Optional[str] = None  # PRINCIPIO 5
    audit_trail: List[str] = field(default_factory=list)  # PRINCIPIO 5


class AtomicNeuroGlyphConverter:
    """
    Converter NEUROGLYPH → Formula con 5 PRINCIPI IMMUTABILI.
    
    PERFORMANCE MASSIME - NO LOOP INFINITI - NO REGEX COMPLESSI
    """
    
    def __init__(self):
        """Inizializza converter atomico."""
        # PRINCIPIO 2: Simboli Unicode unici
        self.logical_symbols = {
            '∧': 'conjunction',
            '∨': 'disjunction', 
            '⇒': 'implication',
            '⇔': 'biconditional',
            '¬': 'negation',
            '∀': 'universal',
            '∃': 'existential',
            '⊢': 'entailment',
            '∴': 'conclusion'
        }
        
        # PRINCIPIO 2: Verifica unicità
        self._verify_uniqueness()
        
        self.conversion_count = 0
    
    def _verify_uniqueness(self):
        """PRINCIPIO 2: Verifica unicità Unicode."""
        symbols = list(self.logical_symbols.keys())
        semantics = list(self.logical_symbols.values())
        
        if len(set(symbols)) != len(symbols):
            raise ValueError("VIOLAZIONE PRINCIPIO 2: Duplicati Unicode")
        
        if len(set(semantics)) != len(semantics):
            raise ValueError("VIOLAZIONE PRINCIPIO 2: Duplicati semantici")
    
    def convert(self, neuroglyph_code: str) -> AtomicConversionResult:
        """
        Converte codice NEUROGLYPH applicando 5 PRINCIPI IMMUTABILI.
        
        Args:
            neuroglyph_code: Espressione simbolica NEUROGLYPH
            
        Returns:
            AtomicConversionResult con tutti i principi applicati
        """
        self.conversion_count += 1
        audit_trail = [f"ATOMIC_CONVERSION_START: {neuroglyph_code}"]
        
        try:
            # PRINCIPIO 1: Tokenizzazione atomica
            tokens = self._tokenize_atomic(neuroglyph_code)
            audit_trail.append(f"ATOMIC_TOKENS: {tokens}")
            
            # PRINCIPIO 4: Parsing semantico
            formula = self._parse_semantic(tokens)
            audit_trail.append(f"SEMANTIC_FORMULA: {formula}")
            
            if formula:
                # PRINCIPIO 1: Estrazione simboli atomica
                symbols_used = self._extract_atomic_symbols(neuroglyph_code)
                audit_trail.append(f"ATOMIC_SYMBOLS: {symbols_used}")
                
                # PRINCIPIO 4: Confidence semantico
                confidence = self._calculate_semantic_confidence(symbols_used)
                audit_trail.append(f"SEMANTIC_CONFIDENCE: {confidence}")
                
                # PRINCIPIO 3: Reversibilità
                reverse_formula = self._create_reverse(formula)
                audit_trail.append(f"REVERSE_FORMULA: {reverse_formula}")
                
                # PRINCIPIO 5: Certificazione
                cert_hash = self._generate_certification(neuroglyph_code, formula)
                audit_trail.append(f"CERTIFICATION: {cert_hash}")
                
                return AtomicConversionResult(
                    formula=formula,
                    success=True,
                    confidence=confidence,
                    symbols_used=symbols_used,
                    reverse_formula=reverse_formula,
                    certification_hash=cert_hash,
                    audit_trail=audit_trail
                )
            else:
                audit_trail.append("PARSING_FAILED")
                return AtomicConversionResult(
                    formula=None,
                    success=False,
                    confidence=0.0,
                    symbols_used=[],
                    audit_trail=audit_trail
                )
        
        except Exception as e:
            audit_trail.append(f"ERROR: {str(e)}")
            return AtomicConversionResult(
                formula=None,
                success=False,
                confidence=0.0,
                symbols_used=[],
                audit_trail=audit_trail
            )
    
    def _tokenize_atomic(self, code: str) -> List[str]:
        """PRINCIPIO 1: Tokenizzazione atomica - 1 simbolo = 1 token."""
        tokens = []
        current_token = ""
        
        for char in code:
            if char in self.logical_symbols:
                # PRINCIPIO 1: Simbolo atomico
                if current_token.strip():
                    tokens.append(current_token.strip())
                    current_token = ""
                tokens.append(char)
            elif char in '():, ':
                # Delimitatori
                if current_token.strip():
                    tokens.append(current_token.strip())
                    current_token = ""
                if char not in ' ':
                    tokens.append(char)
            else:
                current_token += char
        
        if current_token.strip():
            tokens.append(current_token.strip())
        
        return [t for t in tokens if t]
    
    def _parse_semantic(self, tokens: List[str]) -> Optional[Formula]:
        """PRINCIPIO 4: Parsing semantico preciso."""
        if not tokens:
            return None
        
        # Casi semantici comuni
        if len(tokens) == 1:
            return Predicate(tokens[0], [])
        
        elif len(tokens) == 2 and tokens[0] == '¬':
            return Negation(Predicate(tokens[1], []))
        
        elif len(tokens) == 3:
            if tokens[1] == '∧':
                left = Predicate(tokens[0], [])
                right = Predicate(tokens[2], [])
                return Conjunction(left, right)
            elif tokens[1] == '∨':
                left = Predicate(tokens[0], [])
                right = Predicate(tokens[2], [])
                return Disjunction(left, right)
            elif tokens[1] == '⇒':
                left = Predicate(tokens[0], [])
                right = Predicate(tokens[2], [])
                return Implication(left, right)
        
        elif len(tokens) >= 3:
            # Quantificatori
            if tokens[0] == '∀' and ':' in tokens:
                var_name = tokens[1]
                colon_idx = tokens.index(':')
                if colon_idx + 1 < len(tokens):
                    pred_name = tokens[colon_idx + 1]
                    variable = Variable(var_name)
                    formula = Predicate(pred_name, [variable])
                    return Universal(variable, formula)
            
            elif tokens[0] == '∃' and ':' in tokens:
                var_name = tokens[1]
                colon_idx = tokens.index(':')
                if colon_idx + 1 < len(tokens):
                    pred_name = tokens[colon_idx + 1]
                    variable = Variable(var_name)
                    formula = Predicate(pred_name, [variable])
                    return Existential(variable, formula)
        
        # Fallback
        return Predicate(tokens[0], [])
    
    def _extract_atomic_symbols(self, code: str) -> List[str]:
        """PRINCIPIO 1: Estrazione simboli atomica."""
        symbols = []
        for symbol in self.logical_symbols:
            if symbol in code:
                symbols.append(symbol)
        return symbols
    
    def _calculate_semantic_confidence(self, symbols: List[str]) -> float:
        """PRINCIPIO 4: Confidence semantico."""
        if not symbols:
            return 0.5  # Predicato semplice
        
        # Peso basato su simboli logici riconosciuti
        recognized = sum(1 for s in symbols if s in self.logical_symbols)
        total = len(symbols)
        
        return recognized / total if total > 0 else 0.0
    
    def _create_reverse(self, formula: Formula) -> str:
        """PRINCIPIO 3: Reversibilità AST → NEUROGLYPH."""
        if isinstance(formula, Predicate):
            return formula.name
        elif isinstance(formula, Conjunction):
            left = self._create_reverse(formula.left)
            right = self._create_reverse(formula.right)
            return f"{left} ∧ {right}"
        elif isinstance(formula, Disjunction):
            left = self._create_reverse(formula.left)
            right = self._create_reverse(formula.right)
            return f"{left} ∨ {right}"
        elif isinstance(formula, Implication):
            ant = self._create_reverse(formula.antecedent)
            cons = self._create_reverse(formula.consequent)
            return f"{ant} ⇒ {cons}"
        elif isinstance(formula, Negation):
            inner = self._create_reverse(formula.formula)
            return f"¬{inner}"
        elif isinstance(formula, Universal):
            var = str(formula.variable)
            inner = self._create_reverse(formula.formula)
            return f"∀{var}: {inner}"
        elif isinstance(formula, Existential):
            var = str(formula.variable)
            inner = self._create_reverse(formula.formula)
            return f"∃{var}: {inner}"
        else:
            return str(formula)
    
    def _generate_certification(self, code: str, formula: Formula) -> str:
        """PRINCIPIO 5: Certificazione scientifica."""
        timestamp = str(int(time.time()))
        content = f"{code}|{str(formula)}|{timestamp}"
        return hashlib.sha256(content.encode('utf-8')).hexdigest()[:16]


# Factory function
def atomic_convert(neuroglyph_code: str) -> AtomicConversionResult:
    """Converte con converter atomico."""
    converter = AtomicNeuroGlyphConverter()
    return converter.convert(neuroglyph_code)
