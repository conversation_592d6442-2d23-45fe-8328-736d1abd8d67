"""
NEUROGLYPH Formal Logic Engine
API deduce(premises, goal) -> ProofTree
"""

import logging
import math
import time
from typing import List, Dict, Set, Optional, Tuple, Any
from dataclasses import dataclass

# Scipy imports per ottimizzazione profondità e performance
try:
    import scipy.optimize
    import scipy.stats
    import numpy as np
    SCIPY_AVAILABLE = True
except ImportError:
    SCIPY_AVAILABLE = False
    logging.warning("⚠️ SciPy non disponibile - funzionalità avanzate disabilitate")

from .formula import Formula, Variable, Term, Predicate, Implication, Conjunction, Universal
from .proof_tree import ProofTree, ProofStep, ProofRule, KGFactStep
from .unify import SymbolicUnifier, Substitution

logger = logging.getLogger(__name__)


@dataclass
class DeductionResult:
    """Risultato di deduzione logica."""
    
    success: bool
    proof_tree: Optional[ProofTree] = None
    error_message: str = ""
    steps_count: int = 0
    depth: int = 0
    duration: float = 0.0


class FormalLogicEngine:
    """
    Motore di deduzione logica formale.
    
    Features:
    - Modus ponens, universal instantiation
    - Backward chaining goal-driven
    - Proof tree generation
    - Symbolic unification
    """
    
    def __init__(self, max_depth: int = 12, max_steps: int = 200, enable_memoization: bool = True, knowledge_graph=None):
        """
        Inizializza logic engine.

        Args:
            max_depth: Profondità massima reasoning (default 12 per Fase 6.0)
            max_steps: Numero massimo step per proof (default 200 per reasoning complesso)
            enable_memoization: Abilita cache per risultati intermedi
            knowledge_graph: KnowledgeGraph per lookup fatti (Fase 6.0)
        """
        self.max_depth = max_depth
        self.max_steps = max_steps
        self.enable_memoization = enable_memoization
        self.knowledge_graph = knowledge_graph
        self.unifier = SymbolicUnifier()

        # Memoization cache per risultati intermedi (Fase 6.0)
        self._memo_cache: Dict[str, bool] = {}
        self._pruning_threshold = 0.3  # Soglia per pruning euristico
        self._confidence_scores: Dict[str, float] = {}

        # Statistiche estese
        self.proofs_attempted = 0
        self.proofs_successful = 0
        self.cache_hits = 0
        self.cache_misses = 0
        self.pruned_branches = 0

        # Statistiche KG (Fase 6.0)
        self.kg_lookups = 0
        self.kg_hits = 0
        self.kg_misses = 0

        # Performance tracking per depth optimization
        self._depth_performance_history: List[Tuple[int, float, bool]] = []  # (depth, duration, success)
        self._optimal_depth_cache: Dict[str, int] = {}
        self._adaptive_depth_enabled = SCIPY_AVAILABLE

        # Mathematical reasoning enhancements
        self._math_equivalences: Dict[str, List[str]] = {}
        self._symbolic_normalizer = None

        logger.info(f"🧠 FormalLogicEngine inizializzato (max_depth={max_depth}, max_steps={max_steps}, memoization={enable_memoization}, scipy={SCIPY_AVAILABLE})")
    
    def deduce(self, premises: List[Formula], goal: Formula) -> DeductionResult:
        """
        Deduce goal da premises usando backward chaining con profondità ottimale.

        Args:
            premises: Lista premesse
            goal: Formula da dimostrare

        Returns:
            DeductionResult con proof tree se successo
        """
        start_time = time.time()
        self.proofs_attempted += 1

        logger.debug(f"🎯 Tentativo deduzione: {goal}")
        logger.debug(f"   Premesse: {[str(p) for p in premises]}")

        # Calcola profondità ottimale se adaptive depth è abilitato
        if self._adaptive_depth_enabled:
            optimal_depth = self._calculate_optimal_depth(premises, goal)
            original_max_depth = self.max_depth
            self.max_depth = optimal_depth
            logger.debug(f"🎯 Profondità adattiva: {original_max_depth} → {optimal_depth}")

        try:
            # Crea proof tree
            proof_tree = ProofTree(conclusion=goal, premises=premises)

            # Aggiungi premesse al proof tree
            premise_steps = []
            for premise in premises:
                step_id = proof_tree.add_premise(premise)
                premise_steps.append(proof_tree.steps[-1])

            # Backward chaining
            success = self._backward_chain(proof_tree, goal, premise_steps, depth=0)

            duration = time.time() - start_time

            # Registra performance per future ottimizzazioni
            if self._adaptive_depth_enabled:
                self._depth_performance_history.append((self.max_depth, duration, success))
                # Mantieni solo ultimi 50 record per evitare memory leak
                if len(self._depth_performance_history) > 50:
                    self._depth_performance_history = self._depth_performance_history[-50:]

            if success:
                proof_tree.validate()
                self.proofs_successful += 1

                logger.info(f"✅ Deduzione riuscita in {duration:.3f}s ({len(proof_tree.steps)} steps, depth={proof_tree.depth})")

                result = DeductionResult(
                    success=True,
                    proof_tree=proof_tree,
                    steps_count=len(proof_tree.steps),
                    depth=proof_tree.depth,
                    duration=duration
                )
            else:
                logger.warning(f"❌ Deduzione fallita in {duration:.3f}s")

                result = DeductionResult(
                    success=False,
                    error_message="Goal not derivable from premises",
                    duration=duration
                )

        except Exception as e:
            duration = time.time() - start_time

            logger.error(f"❌ Errore deduzione: {e}")

            result = DeductionResult(
                success=False,
                error_message=str(e),
                duration=duration
            )

        finally:
            # Ripristina profondità originale se era stata modificata
            if self._adaptive_depth_enabled and 'original_max_depth' in locals():
                self.max_depth = original_max_depth

        return result
    
    def _backward_chain(self, proof_tree: ProofTree, goal: Formula,
                       available_steps: List[ProofStep], depth: int) -> bool:
        """
        Backward chaining ricorsivo con memoization e pruning (Fase 6.0).

        Args:
            proof_tree: Proof tree in costruzione
            goal: Goal da dimostrare
            available_steps: Step disponibili
            depth: Profondità corrente

        Returns:
            True se goal derivabile
        """
        if depth > self.max_depth:
            logger.debug(f"⚠️ Max depth raggiunta: {depth}")
            return False

        if len(proof_tree.steps) > self.max_steps:
            logger.debug(f"⚠️ Max steps raggiunto: {len(proof_tree.steps)}")
            return False

        # Memoization check (Fase 6.0)
        if self.enable_memoization:
            goal_key = self._get_goal_key(goal, available_steps)
            if goal_key in self._memo_cache:
                self.cache_hits += 1
                logger.debug(f"💾 Cache hit per goal: {goal}")
                return self._memo_cache[goal_key]
            else:
                self.cache_misses += 1

        # Pruning euristico: se depth > soglia e pochi step disponibili, pruna
        if depth > 5 and len(available_steps) < 3:
            confidence = self._calculate_confidence(goal, available_steps)
            if confidence < self._pruning_threshold:
                self.pruned_branches += 1
                logger.debug(f"✂️ Branch pruned (confidence={confidence:.2f}): {goal}")
                if self.enable_memoization:
                    self._memo_cache[goal_key] = False
                return False

        logger.debug(f"🔍 Backward chain (depth={depth}): {goal}")
        
        # 1. Goal già disponibile?
        for step in available_steps:
            if step.formula == goal:
                logger.debug(f"✅ Goal trovato direttamente: {step}")
                return True
        
        # 2. Unificazione migliorata con step disponibili
        for step in available_steps:
            substitution = self._enhanced_unification(step.formula, goal)
            if substitution is not None:
                logger.debug(f"✅ Goal unificabile (enhanced): {step.formula} ≈ {goal}")
                return True

        # 2.5. Knowledge Graph lookup (Fase 6.0)
        if self.knowledge_graph is not None:
            kg_result = self._try_kg_lookup(proof_tree, goal)
            if kg_result:
                logger.debug(f"🔍 Goal trovato nel Knowledge Graph: {goal}")
                return True

        # 3. Modus ponens: cerca P → Goal, poi dimostra P
        for step in available_steps:
            if isinstance(step.formula, Implication):
                if step.formula.consequent == goal:
                    # Trovata implicazione P → Goal, dimostra P
                    antecedent = step.formula.antecedent
                    logger.debug(f"🔗 Modus ponens: dimostra {antecedent} per ottenere {goal}")

                    if self._backward_chain(proof_tree, antecedent, available_steps, depth + 1):
                        # Trova step per antecedent
                        antecedent_step = None
                        for s in proof_tree.steps:
                            if s.formula == antecedent:
                                antecedent_step = s
                                break

                        if antecedent_step:
                            # Applica modus ponens
                            proof_tree.apply_modus_ponens(step, antecedent_step)
                            return True

        # 3.5. Modus ponens con unificazione migliorata: cerca P(x) → Q(x), unifica Q(x) con goal
        for step in available_steps:
            if isinstance(step.formula, Implication):
                # Prova unificazione migliorata del conseguente con il goal
                substitution = self._enhanced_unification(step.formula.consequent, goal)
                if substitution is not None:
                    logger.debug(f"🔗 Modus ponens con unificazione: {step.formula.consequent} ≈ {goal} → {substitution}")

                    # Applica sostituzione all'antecedente
                    instantiated_antecedent = step.formula.antecedent.substitute(substitution.mappings)
                    logger.debug(f"🔗 Antecedente istanziato: {instantiated_antecedent}")

                    # Cerca di dimostrare l'antecedente istanziato
                    if self._backward_chain(proof_tree, instantiated_antecedent, available_steps, depth + 1):
                        # Trova step per antecedente istanziato
                        antecedent_step = None
                        for s in proof_tree.steps:
                            if s.formula == instantiated_antecedent:
                                antecedent_step = s
                                break

                        if antecedent_step:
                            # Crea step per implicazione istanziata
                            instantiated_implication = Implication(instantiated_antecedent, goal)
                            inst_step = proof_tree.add_step(ProofStep(
                                formula=instantiated_implication,
                                rule=ProofRule.UNIVERSAL_INST,
                                premises=[step],
                                substitution=substitution.mappings,
                                justification=f"Universal instantiation: {step.formula} with {substitution}"
                            ))

                            # Applica modus ponens
                            proof_tree.apply_modus_ponens(proof_tree.steps[-1], antecedent_step)
                            return True
        
        # 4. Universal instantiation con unificazione migliorata: cerca ∀x P(x), unifica P(x) con goal
        for step in available_steps:
            if isinstance(step.formula, Universal):
                universal_formula = step.formula

                # Prova unificazione migliorata con formula interna
                substitution = self._enhanced_unification(universal_formula.formula, goal)
                if substitution is not None:
                    # Trova termine per istanziazione
                    var = universal_formula.variable
                    if var in substitution:
                        term = substitution[var]
                        logger.debug(f"🔗 Universal instantiation: {universal_formula} con {term}")

                        proof_tree.apply_universal_instantiation(step, term)
                        return True

                # Prova istanziazione con costanti dal goal
                goal_constants = goal.get_constants()
                for constant in goal_constants:
                    # Istanzia universale con costante
                    instantiated = universal_formula.formula.substitute({universal_formula.variable: constant})
                    logger.debug(f"🔗 Tentativo istanziazione: {universal_formula} con {constant} → {instantiated}")

                    # Se istanziazione produce goal direttamente
                    if instantiated == goal:
                        proof_tree.apply_universal_instantiation(step, constant)
                        return True

                    # Se istanziazione produce implicazione che può portare al goal
                    if isinstance(instantiated, Implication):
                        if instantiated.consequent == goal:
                            # Istanzia e poi cerca di dimostrare antecedente
                            inst_step_id = proof_tree.apply_universal_instantiation(step, constant)
                            inst_step = proof_tree.steps[-1]

                            # Cerca di dimostrare antecedente
                            if self._backward_chain(proof_tree, instantiated.antecedent, available_steps, depth + 1):
                                # Trova step per antecedente
                                antecedent_step = None
                                for s in proof_tree.steps:
                                    if s.formula == instantiated.antecedent:
                                        antecedent_step = s
                                        break

                                if antecedent_step:
                                    # Applica modus ponens
                                    proof_tree.apply_modus_ponens(inst_step, antecedent_step)
                                    return True
        
        # 5. Conjunction elimination: cerca P ∧ Q, goal = P o Q
        for step in available_steps:
            if isinstance(step.formula, Conjunction):
                conjunction = step.formula
                
                if conjunction.left == goal:
                    logger.debug(f"🔗 Conjunction elimination (left): {conjunction} ⊢ {goal}")
                    proof_tree.apply_conjunction_elimination(step, left=True)
                    return True
                
                if conjunction.right == goal:
                    logger.debug(f"🔗 Conjunction elimination (right): {conjunction} ⊢ {goal}")
                    proof_tree.apply_conjunction_elimination(step, left=False)
                    return True
        
        # 6. Conjunction introduction: goal = P ∧ Q, dimostra P e Q
        if isinstance(goal, Conjunction):
            left_goal = goal.left
            right_goal = goal.right
            
            logger.debug(f"🔗 Conjunction introduction: dimostra {left_goal} e {right_goal}")
            
            # Dimostra entrambi i lati
            left_success = self._backward_chain(proof_tree, left_goal, available_steps, depth + 1)
            right_success = self._backward_chain(proof_tree, right_goal, available_steps, depth + 1)
            
            if left_success and right_success:
                # Trova step per entrambi i lati
                left_step = None
                right_step = None
                
                for s in proof_tree.steps:
                    if s.formula == left_goal:
                        left_step = s
                    if s.formula == right_goal:
                        right_step = s
                
                if left_step and right_step:
                    proof_tree.apply_conjunction_introduction(left_step, right_step)
                    return True
        
        logger.debug(f"❌ Goal non derivabile: {goal}")

        # Salva risultato in cache
        if self.enable_memoization:
            goal_key = self._get_goal_key(goal, available_steps)
            self._memo_cache[goal_key] = False

        return False
    
    def _get_goal_key(self, goal: Formula, available_steps: List[ProofStep]) -> str:
        """Genera chiave univoca per memoization."""
        # Combina goal con hash degli step disponibili
        steps_hash = hash(tuple(str(step.formula) for step in available_steps))
        return f"{str(goal)}:{steps_hash}"

    def _calculate_confidence(self, goal: Formula, available_steps: List[ProofStep]) -> float:
        """Calcola confidence score per pruning euristico."""
        # Fattori che aumentano confidence:
        # 1. Numero di step disponibili
        # 2. Presenza di implicazioni che portano al goal
        # 3. Presenza di universali che possono essere istanziati

        confidence = 0.0

        # Base confidence da numero step
        confidence += min(len(available_steps) * 0.1, 0.5)

        # Bonus per implicazioni rilevanti
        for step in available_steps:
            if isinstance(step.formula, Implication):
                if step.formula.consequent == goal:
                    confidence += 0.3
                elif str(goal) in str(step.formula.consequent):
                    confidence += 0.1

        # Bonus per universali rilevanti
        for step in available_steps:
            if isinstance(step.formula, Universal):
                if str(goal) in str(step.formula.formula):
                    confidence += 0.2

        return min(confidence, 1.0)

    def _calculate_optimal_depth(self, premises: List[Formula], goal: Formula) -> int:
        """
        Calcola profondità ottimale per il problema specifico usando scipy.optimize.

        Args:
            premises: Lista premesse
            goal: Formula goal

        Returns:
            Profondità ottimale calcolata
        """
        if not SCIPY_AVAILABLE:
            return self.max_depth

        # Genera chiave per cache
        problem_key = f"{len(premises)}:{str(goal)[:50]}"
        if problem_key in self._optimal_depth_cache:
            return self._optimal_depth_cache[problem_key]

        # Analizza complessità del problema
        complexity_score = self._analyze_problem_complexity(premises, goal)

        # Usa storia performance per predire profondità ottimale
        if len(self._depth_performance_history) >= 5:
            # Estrai dati storici
            depths = [h[0] for h in self._depth_performance_history]
            durations = [h[1] for h in self._depth_performance_history]
            successes = [1.0 if h[2] else 0.0 for h in self._depth_performance_history]

            try:
                # Trova profondità che massimizza success_rate / duration
                def objective(depth_val):
                    # Interpola success rate e duration per questa profondità
                    if len(depths) < 2:
                        return -1.0

                    success_interp = np.interp(depth_val, depths, successes)
                    duration_interp = np.interp(depth_val, depths, durations)

                    # Evita divisione per zero
                    if duration_interp <= 0:
                        return -1.0

                    # Massimizza efficienza (success/time) con penalty per profondità eccessive
                    efficiency = success_interp / duration_interp
                    depth_penalty = max(0, (depth_val - 15) * 0.1)  # Penalizza depth > 15

                    return -(efficiency - depth_penalty)  # Negativo perché minimize

                # Ottimizza nel range [3, 25]
                result = scipy.optimize.minimize_scalar(
                    objective,
                    bounds=(3, 25),
                    method='bounded'
                )

                optimal_depth = max(3, min(25, int(result.x)))

            except Exception as e:
                logger.debug(f"⚠️ Errore ottimizzazione profondità: {e}")
                optimal_depth = min(12 + complexity_score, 20)
        else:
            # Non abbastanza dati storici, usa euristica basata su complessità
            optimal_depth = min(8 + complexity_score, 18)

        # Cache risultato
        self._optimal_depth_cache[problem_key] = optimal_depth

        logger.debug(f"🎯 Profondità ottimale calcolata: {optimal_depth} (complessità: {complexity_score})")
        return optimal_depth

    def _analyze_problem_complexity(self, premises: List[Formula], goal: Formula) -> int:
        """
        Analizza complessità del problema di ragionamento.

        Returns:
            Score di complessità (0-10)
        """
        complexity = 0

        # Fattore 1: Numero premesse
        complexity += min(len(premises) // 2, 3)

        # Fattore 2: Profondità nesting formule
        max_nesting = 0
        for formula in premises + [goal]:
            nesting = self._calculate_formula_nesting(formula)
            max_nesting = max(max_nesting, nesting)
        complexity += min(max_nesting, 3)

        # Fattore 3: Presenza di quantificatori
        has_universals = any(isinstance(f, Universal) for f in premises)
        if has_universals:
            complexity += 2

        # Fattore 4: Numero variabili uniche
        all_vars = set()
        for formula in premises + [goal]:
            all_vars.update(formula.get_variables())
        complexity += min(len(all_vars) // 2, 2)

        return min(complexity, 10)

    def _calculate_formula_nesting(self, formula: Formula) -> int:
        """Calcola profondità di nesting di una formula."""
        if isinstance(formula, (Implication, Conjunction)):
            left_depth = self._calculate_formula_nesting(formula.left) if hasattr(formula, 'left') else 0
            right_depth = self._calculate_formula_nesting(formula.right) if hasattr(formula, 'right') else 0
            return 1 + max(left_depth, right_depth)
        elif isinstance(formula, Universal):
            return 1 + self._calculate_formula_nesting(formula.formula)
        else:
            return 1

    def _enhanced_unification(self, formula1: Formula, formula2: Formula) -> Optional[Substitution]:
        """
        Unificazione migliorata con supporto per equivalenze matematiche.

        Args:
            formula1: Prima formula
            formula2: Seconda formula

        Returns:
            Substitution se unificabili (anche tramite equivalenze), None altrimenti
        """
        # Prima prova unificazione standard
        standard_unification = self.unifier.unify_formulas(formula1, formula2)
        if standard_unification is not None:
            return standard_unification

        # Se fallisce, prova con equivalenze matematiche
        if SCIPY_AVAILABLE:
            return self._try_mathematical_equivalence(formula1, formula2)

        return None

    def _try_mathematical_equivalence(self, formula1: Formula, formula2: Formula) -> Optional[Substitution]:
        """
        Tenta unificazione tramite equivalenze matematiche.

        Args:
            formula1: Prima formula
            formula2: Seconda formula

        Returns:
            Substitution se equivalenti matematicamente, None altrimenti
        """
        try:
            # Normalizza entrambe le formule
            norm1 = self._normalize_mathematical_expression(formula1)
            norm2 = self._normalize_mathematical_expression(formula2)

            # Prova unificazione su forme normalizzate
            if norm1 != formula1 or norm2 != formula2:
                return self.unifier.unify_formulas(norm1, norm2)

            # Prova equivalenze specifiche (es. commutatività)
            return self._try_commutative_equivalence(formula1, formula2)

        except Exception as e:
            logger.debug(f"⚠️ Errore equivalenza matematica: {e}")
            return None

    def _normalize_mathematical_expression(self, formula: Formula) -> Formula:
        """
        Normalizza espressioni matematiche per facilitare unificazione.

        Args:
            formula: Formula da normalizzare

        Returns:
            Formula normalizzata
        """
        # Per ora implementazione base - può essere estesa con SymPy
        if isinstance(formula, Predicate):
            # Ordina argomenti se il predicato è commutativo
            if formula.name in ['equals', 'add', 'multiply', 'and', 'or']:
                if len(formula.args) == 2:
                    # Ordina lessicograficamente per consistenza
                    sorted_args = sorted(formula.args, key=str)
                    if sorted_args != list(formula.args):
                        return Predicate(formula.name, sorted_args)

        return formula

    def _try_commutative_equivalence(self, formula1: Formula, formula2: Formula) -> Optional[Substitution]:
        """
        Tenta unificazione considerando proprietà commutative.

        Args:
            formula1: Prima formula
            formula2: Seconda formula

        Returns:
            Substitution se equivalenti per commutatività, None altrimenti
        """
        if isinstance(formula1, Predicate) and isinstance(formula2, Predicate):
            if formula1.name == formula2.name and len(formula1.args) == len(formula2.args) == 2:
                # Prova scambio argomenti per predicati commutativi
                if formula1.name in ['equals', 'add', 'multiply']:
                    swapped_formula2 = Predicate(formula2.name, [formula2.args[1], formula2.args[0]])
                    return self.unifier.unify_formulas(formula1, swapped_formula2)

        return None

    def _try_kg_lookup(self, proof_tree: ProofTree, goal: Formula) -> bool:
        """
        Tenta lookup del goal nel Knowledge Graph.

        Args:
            proof_tree: Proof tree in costruzione
            goal: Goal da cercare nel KG

        Returns:
            True se goal trovato nel KG e aggiunto al proof tree
        """
        if self.knowledge_graph is None:
            return False

        self.kg_lookups += 1

        try:
            # Converti goal in query KG
            # Per ora supportiamo solo predicati semplici
            if hasattr(goal, 'name') and hasattr(goal, 'args'):
                # Predicato: P(a, b) -> subject=a, predicate=P, object=b
                if len(goal.args) == 1:
                    # Predicato unario: P(a) -> subject=a, predicate=P
                    subject = str(goal.args[0])
                    predicate = goal.name
                    facts = self.knowledge_graph.lookup_facts(subject=subject, predicate=predicate)
                elif len(goal.args) == 2:
                    # Predicato binario: P(a, b) -> subject=a, predicate=P, object=b
                    subject = str(goal.args[0])
                    predicate = goal.name
                    object_val = str(goal.args[1])
                    facts = self.knowledge_graph.lookup_facts(subject=subject, predicate=predicate, object=object_val)
                else:
                    # Predicati con più argomenti non supportati per ora
                    facts = []
            else:
                # Formula complessa, prova lookup generico
                facts = self.knowledge_graph.lookup_facts(predicate=str(goal))

            if facts:
                # Trovato almeno un fatto nel KG
                self.kg_hits += 1

                # Usa il primo fatto trovato
                fact = facts[0]

                # Aggiungi step KG al proof tree
                proof_tree.add_kg_fact(
                    formula=goal,
                    fact_id=fact.id,
                    confidence=fact.confidence,
                    justification=f"Knowledge Graph lookup: {fact.subject} {fact.predicate} {fact.object}"
                )

                logger.debug(f"🔍 KG hit: {goal} -> fact #{fact.id}")
                return True
            else:
                # Nessun fatto trovato
                self.kg_misses += 1
                logger.debug(f"🔍 KG miss: {goal}")
                return False

        except Exception as e:
            logger.error(f"❌ Errore KG lookup per {goal}: {e}")
            self.kg_misses += 1
            return False

    def get_statistics(self) -> Dict[str, Any]:
        """Ottiene statistiche engine estese con performance analytics."""
        success_rate = self.proofs_successful / self.proofs_attempted if self.proofs_attempted > 0 else 0.0
        cache_hit_rate = self.cache_hits / (self.cache_hits + self.cache_misses) if (self.cache_hits + self.cache_misses) > 0 else 0.0
        kg_hit_rate = self.kg_hits / self.kg_lookups if self.kg_lookups > 0 else 0.0

        # Statistiche performance avanzate
        performance_stats = self._calculate_performance_statistics()

        base_stats = {
            'proofs_attempted': self.proofs_attempted,
            'proofs_successful': self.proofs_successful,
            'success_rate': success_rate,
            'max_depth': self.max_depth,
            'max_steps': self.max_steps,
            'cache_hits': self.cache_hits,
            'cache_misses': self.cache_misses,
            'cache_hit_rate': cache_hit_rate,
            'pruned_branches': self.pruned_branches,
            'memo_cache_size': len(self._memo_cache),
            'kg_lookups': self.kg_lookups,
            'kg_hits': self.kg_hits,
            'kg_misses': self.kg_misses,
            'kg_hit_rate': kg_hit_rate,
            'adaptive_depth_enabled': self._adaptive_depth_enabled,
            'scipy_available': SCIPY_AVAILABLE
        }

        # Merge con statistiche performance
        base_stats.update(performance_stats)
        return base_stats

    def _calculate_performance_statistics(self) -> Dict[str, Any]:
        """Calcola statistiche performance avanzate."""
        if not self._depth_performance_history:
            return {
                'avg_duration': 0.0,
                'avg_depth_used': 0.0,
                'depth_efficiency': 0.0,
                'performance_trend': 'no_data'
            }

        durations = [h[1] for h in self._depth_performance_history]
        depths = [h[0] for h in self._depth_performance_history]
        successes = [h[2] for h in self._depth_performance_history]

        avg_duration = sum(durations) / len(durations)
        avg_depth = sum(depths) / len(depths)
        success_count = sum(successes)

        # Calcola efficienza profondità (successi per unità di profondità)
        depth_efficiency = success_count / sum(depths) if sum(depths) > 0 else 0.0

        # Analizza trend performance (ultimi 10 vs precedenti)
        performance_trend = 'stable'
        if len(self._depth_performance_history) >= 10:
            recent_success_rate = sum(successes[-10:]) / 10
            older_success_rate = sum(successes[:-10]) / len(successes[:-10]) if len(successes) > 10 else recent_success_rate

            if recent_success_rate > older_success_rate + 0.1:
                performance_trend = 'improving'
            elif recent_success_rate < older_success_rate - 0.1:
                performance_trend = 'declining'

        return {
            'avg_duration': avg_duration,
            'avg_depth_used': avg_depth,
            'depth_efficiency': depth_efficiency,
            'performance_trend': performance_trend,
            'total_performance_samples': len(self._depth_performance_history)
        }
    
    def reset_statistics(self):
        """Reset statistiche e cache con performance history."""
        self.proofs_attempted = 0
        self.proofs_successful = 0
        self.cache_hits = 0
        self.cache_misses = 0
        self.pruned_branches = 0
        self.kg_lookups = 0
        self.kg_hits = 0
        self.kg_misses = 0
        self._memo_cache.clear()
        self._confidence_scores.clear()
        self._depth_performance_history.clear()
        self._optimal_depth_cache.clear()

        logger.info("🔄 Statistiche FormalLogicEngine resettate")


# Factory functions per uso rapido con funzionalità avanzate
def prove(premises: List[Formula], goal: Formula,
          max_depth: int = 12, max_steps: int = 200,
          adaptive_depth: bool = True) -> DeductionResult:
    """
    Prova goal da premises con engine ottimizzato.

    Args:
        premises: Lista premesse
        goal: Formula da dimostrare
        max_depth: Profondità massima (può essere adattata dinamicamente)
        max_steps: Step massimi
        adaptive_depth: Abilita calcolo dinamico profondità ottimale

    Returns:
        DeductionResult con statistiche performance
    """
    engine = FormalLogicEngine(max_depth=max_depth, max_steps=max_steps)

    # Disabilita adaptive depth se richiesto o se scipy non disponibile
    if not adaptive_depth or not SCIPY_AVAILABLE:
        engine._adaptive_depth_enabled = False

    return engine.deduce(premises, goal)


def prove_with_profiling(premises: List[Formula], goal: Formula,
                        max_depth: int = 12, max_steps: int = 200) -> Tuple[DeductionResult, Dict[str, Any]]:
    """
    Prova goal da premises con profiling dettagliato.

    Args:
        premises: Lista premesse
        goal: Formula da dimostrare
        max_depth: Profondità massima
        max_steps: Step massimi

    Returns:
        Tuple di (DeductionResult, statistiche_dettagliate)
    """
    engine = FormalLogicEngine(max_depth=max_depth, max_steps=max_steps)
    result = engine.deduce(premises, goal)
    stats = engine.get_statistics()

    return result, stats
