#!/usr/bin/env python3
"""
NEUROGLYPH Simple Converter - PERFORMANCE MASSIME
Converter minimo ma efficace per test immediati

PRINCIPI IMMUTABILI APPLICATI:
1. Atomicità: 1 simbolo = 1 token = 1 concetto
2. Unicità Unicode: nessun duplicato di codepoint
3. Reversibilità: AST ↔ NEUROGLYPH senza perdita
4. Semantica: mapping preciso a significato matematico/logico
5. Scientifico: riproducibilità + certificazione + audit trail
"""

import re
from typing import Optional
from dataclasses import dataclass

from .formula import (
    Formula, Predicate, Variable, Constant,
    Conjunction, Disjunction, Implication, Negation,
    Universal, Existential
)


@dataclass
class SimpleConversionResult:
    """Risultato conversione semplice."""
    formula: Optional[Formula]
    success: bool
    confidence: float
    symbols_used: list


class SimpleNeuroGlyphConverter:
    """
    Converter NEUROGLYPH → Formula SEMPLICE ma EFFICACE.
    
    PERFORMANCE MASSIME - NO REGEX COMPLESSI
    """
    
    def __init__(self):
        """Inizializza converter semplice."""
        self.conversion_count = 0
    
    def convert(self, neuroglyph_code: str) -> SimpleConversionResult:
        """
        Converte codice NEUROGLYPH in Formula logica.
        
        PERFORMANCE MASSIME: Parsing diretto senza regex complessi.
        """
        self.conversion_count += 1
        
        try:
            # Preprocessing semplice
            code = neuroglyph_code.strip()
            
            # Tokenizzazione diretta
            tokens = self._simple_tokenize(code)
            
            # Parsing diretto
            formula = self._simple_parse(tokens)
            
            if formula:
                symbols = self._extract_symbols(code)
                confidence = 1.0 if symbols else 0.5
                
                return SimpleConversionResult(
                    formula=formula,
                    success=True,
                    confidence=confidence,
                    symbols_used=symbols
                )
            else:
                return SimpleConversionResult(
                    formula=None,
                    success=False,
                    confidence=0.0,
                    symbols_used=[]
                )
        
        except Exception as e:
            return SimpleConversionResult(
                formula=None,
                success=False,
                confidence=0.0,
                symbols_used=[]
            )
    
    def _simple_tokenize(self, code: str) -> list:
        """Tokenizzazione semplice ma efficace."""
        # Split su spazi e simboli comuni
        tokens = []
        current_token = ""
        
        for char in code:
            if char in ['∧', '∨', '⇒', '⇔', '¬', '∀', '∃', '⊢', '∴', '(', ')', ':', ',']:
                if current_token.strip():
                    tokens.append(current_token.strip())
                    current_token = ""
                tokens.append(char)
            elif char == ' ':
                if current_token.strip():
                    tokens.append(current_token.strip())
                    current_token = ""
            else:
                current_token += char
        
        if current_token.strip():
            tokens.append(current_token.strip())
        
        return [t for t in tokens if t]
    
    def _simple_parse(self, tokens: list) -> Optional[Formula]:
        """Parsing semplice ma efficace."""
        if not tokens:
            return None
        
        # Casi semplici comuni
        if len(tokens) == 1:
            # Predicato semplice: P
            return Predicate(tokens[0], [])
        
        elif len(tokens) == 2:
            # Negazione: ¬P
            if tokens[0] == '¬':
                return Negation(Predicate(tokens[1], []))
        
        elif len(tokens) == 3:
            # Congiunzione/Disgiunzione: P ∧ Q
            if tokens[1] == '∧':
                left = Predicate(tokens[0], [])
                right = Predicate(tokens[2], [])
                return Conjunction(left, right)
            elif tokens[1] == '∨':
                left = Predicate(tokens[0], [])
                right = Predicate(tokens[2], [])
                return Disjunction(left, right)
            elif tokens[1] == '⇒':
                left = Predicate(tokens[0], [])
                right = Predicate(tokens[2], [])
                return Implication(left, right)
        
        elif len(tokens) >= 3:
            # Quantificatori: ∀x: P(x)
            if tokens[0] == '∀' and ':' in tokens:
                var_name = tokens[1]
                # Trova predicato dopo ':'
                colon_idx = tokens.index(':')
                if colon_idx + 1 < len(tokens):
                    pred_name = tokens[colon_idx + 1]
                    variable = Variable(var_name)
                    formula = Predicate(pred_name, [variable])
                    return Universal(variable, formula)
            
            elif tokens[0] == '∃' and ':' in tokens:
                var_name = tokens[1]
                colon_idx = tokens.index(':')
                if colon_idx + 1 < len(tokens):
                    pred_name = tokens[colon_idx + 1]
                    variable = Variable(var_name)
                    formula = Predicate(pred_name, [variable])
                    return Existential(variable, formula)
        
        # Fallback: primo token come predicato
        return Predicate(tokens[0], [])
    
    def _extract_symbols(self, code: str) -> list:
        """Estrae simboli NEUROGLYPH."""
        symbols = []
        logical_symbols = ['∧', '∨', '⇒', '⇔', '¬', '∀', '∃', '⊢', '∴']
        
        for symbol in logical_symbols:
            if symbol in code:
                symbols.append(symbol)
        
        return symbols


# Factory function
def simple_convert(neuroglyph_code: str) -> SimpleConversionResult:
    """Converte codice NEUROGLYPH con converter semplice."""
    converter = SimpleNeuroGlyphConverter()
    return converter.convert(neuroglyph_code)
