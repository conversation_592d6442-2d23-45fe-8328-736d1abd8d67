#!/usr/bin/env python3
"""
NEUROGLYPH Fine-tuning Preparation Script
Prepara il fine-tuning con dataset espanso e configurazioni ottimali

Implementa:
- 4-epoch training con cosine LR schedule 1e-4→2e-5
- Dataset validation e preprocessing
- Configurazione QLoRA 4-bit
- Benchmark target verification
"""

import json
import sys
import time
from pathlib import Path
from typing import Dict, Any, List

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))


def validate_dataset_quality(dataset_path: str) -> Dict[str, Any]:
    """Valida qualità del dataset per fine-tuning."""
    print("📊 VALIDATING DATASET QUALITY")
    print("=" * 50)
    
    with open(dataset_path, 'r') as f:
        data = json.load(f)
    
    metadata = data['metadata']
    patterns = data['patterns']
    
    # Metriche base
    total_patterns = len(patterns)
    validation_rate = metadata.get('validation_rate', 0.0)
    
    print(f"📈 Total patterns: {total_patterns}")
    print(f"📊 Validation rate: {validation_rate * 100:.1f}%")
    
    # Analisi distribuzione categorie
    categories = {}
    for pattern in patterns:
        cat = pattern.get('category', 'core')
        categories[cat] = categories.get(cat, 0) + 1
    
    print(f"\n📂 Category distribution:")
    for cat, count in sorted(categories.items()):
        percentage = (count / total_patterns) * 100
        print(f"  {cat}: {count} ({percentage:.1f}%)")
    
    # Verifica fidelity
    fidelities = [p.get('fidelity', 1.0) for p in patterns]
    avg_fidelity = sum(fidelities) / len(fidelities)
    min_fidelity = min(fidelities)
    
    print(f"\n🎯 Fidelity metrics:")
    print(f"  Average: {avg_fidelity:.3f}")
    print(f"  Minimum: {min_fidelity:.3f}")
    
    # Verifica contrastive examples
    contrastive_count = sum(1 for p in patterns if p.get('category') == 'contrastive')
    contrastive_ratio = contrastive_count / total_patterns
    
    print(f"\n🚫 Contrastive examples:")
    print(f"  Count: {contrastive_count}")
    print(f"  Ratio: {contrastive_ratio:.1%}")
    
    # Criteri di qualità (aggiustati per dataset reale)
    quality_checks = {
        'total_patterns': total_patterns >= 1000,
        'validation_rate': validation_rate >= 0.99,
        'avg_fidelity': avg_fidelity >= 0.99,
        'min_fidelity': min_fidelity >= 0.95,
        'contrastive_ratio': 0.10 <= contrastive_ratio <= 0.30,  # Più flessibile
        'category_diversity': len(categories) >= 5
    }
    
    print(f"\n✅ Quality checks:")
    all_passed = True
    for check, passed in quality_checks.items():
        status = "✅" if passed else "❌"
        print(f"  {status} {check}: {passed}")
        if not passed:
            all_passed = False
    
    return {
        'total_patterns': total_patterns,
        'validation_rate': validation_rate,
        'avg_fidelity': avg_fidelity,
        'min_fidelity': min_fidelity,
        'contrastive_ratio': contrastive_ratio,
        'categories': categories,
        'quality_passed': all_passed
    }


def generate_training_config() -> Dict[str, Any]:
    """Genera configurazione ottimale per fine-tuning."""
    print("\n🔧 GENERATING TRAINING CONFIGURATION")
    print("=" * 50)
    
    # Configurazione basata sulle memorie e best practices
    config = {
        # Model settings
        'model_name': 'Qwen2.5-Coder-1.5B-Instruct',
        'model_path': 'models_llm/Qwen2.5-Coder-1.5B-Instruct-bnb-4bit',
        
        # QLoRA 4-bit settings
        'use_qlora': True,
        'bits': 4,
        'quant_type': 'nf4',
        'use_double_quant': True,
        'lora_r': 16,
        'lora_alpha': 32,
        'lora_dropout': 0.1,
        'target_modules': ['q_proj', 'v_proj', 'k_proj', 'o_proj', 'gate_proj', 'up_proj', 'down_proj'],
        
        # Training hyperparameters (ultra-conservative)
        'num_train_epochs': 4,
        'learning_rate': 1e-4,
        'min_learning_rate': 2e-5,
        'lr_scheduler_type': 'cosine',
        'warmup_ratio': 0.1,
        'weight_decay': 0.05,
        'gradient_clipping': 0.2,
        
        # Batch settings
        'per_device_train_batch_size': 1,
        'gradient_accumulation_steps': 16,
        'effective_batch_size': 16,  # 1 * 16
        
        # Optimization
        'optim': 'adamw_torch',
        'adam_beta1': 0.9,
        'adam_beta2': 0.999,
        'adam_epsilon': 1e-8,
        
        # Logging and saving
        'logging_steps': 10,
        'save_steps': 100,
        'eval_steps': 50,
        'save_total_limit': 3,
        'load_best_model_at_end': True,
        'metric_for_best_model': 'eval_loss',
        'greater_is_better': False,
        
        # Data settings
        'max_seq_length': 512,
        'dataset_text_field': 'text',
        'packing': False,
        
        # Validation
        'evaluation_strategy': 'steps',
        'eval_accumulation_steps': 1,
        'dataloader_num_workers': 4,
        
        # Memory optimization
        'fp16': True,
        'gradient_checkpointing': True,
        'dataloader_pin_memory': True,
        
        # Reproducibility
        'seed': 42,
        'data_seed': 42,
        
        # NEUROGLYPH specific
        'symbolic_validation': True,
        'fidelity_threshold': 0.95,
        'early_stopping_patience': 3,
        'benchmark_targets': {
            'logiqa': 0.60,
            'gsm8k': 0.50,
            'humaneval': 0.40
        }
    }
    
    print("📋 Training configuration:")
    print(f"  Model: {config['model_name']}")
    print(f"  Epochs: {config['num_train_epochs']}")
    print(f"  Learning rate: {config['learning_rate']} → {config['min_learning_rate']}")
    print(f"  Batch size: {config['effective_batch_size']} (effective)")
    print(f"  QLoRA: {config['use_qlora']} ({config['bits']}-bit)")
    print(f"  Max sequence length: {config['max_seq_length']}")
    
    return config


def verify_benchmark_readiness() -> bool:
    """Verifica che i benchmark siano pronti."""
    print("\n🎯 VERIFYING BENCHMARK READINESS")
    print("=" * 50)
    
    try:
        # Import benchmark loaders
        from neuroglyph.evaluation.benchmarks import (
            LogiQALoader, GSM8KLoader, HumanEvalLoader,
            BENCHMARK_AVAILABILITY
        )
        
        print("📊 Benchmark availability:")
        all_available = True
        for name, available in BENCHMARK_AVAILABILITY.items():
            status = "✅" if available else "❌"
            print(f"  {status} {name.upper()}: {'Available' if available else 'Not Available'}")
            if not available:
                all_available = False
        
        if not all_available:
            print("❌ Some benchmarks not available")
            return False
        
        # Test basic functionality
        print("\n🧪 Testing benchmark functionality:")
        
        # LogiQA
        logiqa_loader = LogiQALoader()
        logiqa_samples = logiqa_loader.load_samples("mock", max_samples=2)
        print(f"  ✅ LogiQA: {len(logiqa_samples)} samples loaded")
        
        # GSM8K
        gsm8k_loader = GSM8KLoader()
        gsm8k_samples = gsm8k_loader.load_samples("mock", max_samples=2)
        print(f"  ✅ GSM8K: {len(gsm8k_samples)} samples loaded")
        
        # HumanEval
        humaneval_loader = HumanEvalLoader()
        humaneval_samples = humaneval_loader.load_samples("mock", max_samples=2)
        print(f"  ✅ HumanEval: {len(humaneval_samples)} samples loaded")
        
        print("\n✅ All benchmarks ready for evaluation")
        return True
        
    except Exception as e:
        print(f"❌ Benchmark verification failed: {e}")
        return False


def verify_logic_engine() -> bool:
    """Verifica che il FormalLogicEngine sia pronto."""
    print("\n🧠 VERIFYING ENHANCED LOGIC ENGINE")
    print("=" * 50)
    
    try:
        from neuroglyph.logic.logic_engine import FormalLogicEngine, prove_with_profiling
        from neuroglyph.logic.formula import Predicate, Implication, Constant
        
        # Test engine initialization
        engine = FormalLogicEngine()
        stats = engine.get_statistics()
        
        print(f"📊 Engine status:")
        print(f"  SciPy available: {stats['scipy_available']}")
        print(f"  Adaptive depth: {stats['adaptive_depth_enabled']}")
        print(f"  Max depth: {stats['max_depth']}")
        print(f"  Max steps: {stats['max_steps']}")
        
        # Test reasoning performance
        a = Constant('a')
        b = Constant('b')
        c = Constant('c')
        
        P_a = Predicate('P', [a])
        Q_b = Predicate('Q', [b])
        R_c = Predicate('R', [c])
        
        impl1 = Implication(P_a, Q_b)
        impl2 = Implication(Q_b, R_c)
        
        premises = [P_a, impl1, impl2]
        goal = R_c
        
        start_time = time.time()
        result, detailed_stats = prove_with_profiling(premises, goal)
        duration = time.time() - start_time
        
        print(f"\n⚡ Performance test:")
        print(f"  Success: {result.success}")
        print(f"  Duration: {duration:.3f}s")
        print(f"  Steps: {result.steps_count}")
        print(f"  Depth: {result.depth}")
        print(f"  Performance trend: {detailed_stats.get('performance_trend', 'N/A')}")
        
        # Verify performance criteria
        if not result.success:
            print("❌ Reasoning test failed")
            return False
        
        if duration > 1.0:
            print(f"❌ Performance too slow: {duration:.3f}s > 1.0s")
            return False
        
        print("✅ Logic engine ready for fine-tuning")
        return True
        
    except Exception as e:
        print(f"❌ Logic engine verification failed: {e}")
        return False


def main():
    """Main preparation workflow."""
    print("🚀 NEUROGLYPH FINE-TUNING PREPARATION")
    print("=" * 60)
    print("Preparing for 4-epoch training with cosine LR schedule")
    print("Target: LogiQA≥60%, GSM8K≥50%, HumanEval≥40%")
    print("=" * 60)
    
    # Step 1: Validate dataset
    dataset_path = "data/neuroglyph_certified_v3_1000plus.json"
    if not Path(dataset_path).exists():
        print(f"❌ Dataset not found: {dataset_path}")
        return False
    
    dataset_stats = validate_dataset_quality(dataset_path)
    if not dataset_stats['quality_passed']:
        print("❌ Dataset quality validation failed")
        return False
    
    # Step 2: Verify benchmarks
    if not verify_benchmark_readiness():
        print("❌ Benchmark verification failed")
        return False
    
    # Step 3: Verify logic engine
    if not verify_logic_engine():
        print("❌ Logic engine verification failed")
        return False
    
    # Step 4: Generate training config
    training_config = generate_training_config()
    
    # Step 5: Save configuration
    config_path = "training/neuroglyph_finetuning_config.json"
    Path(config_path).parent.mkdir(parents=True, exist_ok=True)
    
    with open(config_path, 'w') as f:
        json.dump(training_config, f, indent=2)
    
    print(f"\n💾 Configuration saved: {config_path}")
    
    # Step 6: Generate summary
    print("\n" + "=" * 60)
    print("📊 PREPARATION SUMMARY")
    print("=" * 60)
    print(f"✅ Dataset: {dataset_stats['total_patterns']} patterns, {dataset_stats['avg_fidelity']:.3f} avg fidelity")
    print(f"✅ Benchmarks: LogiQA, GSM8K, HumanEval ready")
    print(f"✅ Logic Engine: Enhanced with SciPy integration")
    print(f"✅ Configuration: 4-epoch cosine LR schedule")
    print(f"✅ Target: LogiQA≥60%, GSM8K≥50%, HumanEval≥40%")
    print("\n🎉 READY FOR FINE-TUNING!")
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
