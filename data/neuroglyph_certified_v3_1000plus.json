{"metadata": {"name": "NEUROGLYPH Certified Dataset v3.0 (1000+ Patterns)", "description": "Massively expanded dataset with 1000+ patterns and 20% contrastive examples", "parser": "Formal grammar-based", "certification_date": "2025-06-09", "total_patterns": 1116, "validated_patterns": 1116, "failed_patterns": 0, "validation_rate": 1.0, "min_fidelity_required": 0.95, "audit_lock_verified": true, "expansion_info": {"original_patterns": 250, "generated_patterns": 866, "expansion_ratio": 4.464, "contrastive_patterns": 150, "contrastive_ratio": 0.17321016166281755}, "expansion_date": "2025-06-09T12:47:15.463581"}, "patterns": [{"input": "∀x: P(x)", "output": "∀x: P(x)", "fidelity": 1.0, "ast_type": "UniversalQuantification"}, {"input": "∃x: P(x)", "output": "∃x: P(x)", "fidelity": 1.0, "ast_type": "ExistentialQuantification"}, {"input": "∀x ∈ ℝ: P(x)", "output": "∀x ∈ ℝ: P(x)", "fidelity": 1.0, "ast_type": "UniversalQuantification"}, {"input": "∃x ∈ ℕ: P(x)", "output": "∃x ∈ ℕ: P(x)", "fidelity": 1.0, "ast_type": "ExistentialQuantification"}, {"input": "∀x ∃y: R(x,y)", "output": "∀x ∃y: R(x,y)", "fidelity": 1.0, "ast_type": "UniversalQuantification"}, {"input": "P ⇒ Q", "output": "P ⇒ Q", "fidelity": 1.0, "ast_type": "MaterialImplication"}, {"input": "P ∧ Q", "output": "P ∧ Q", "fidelity": 1.0, "ast_type": "LogicalConjunction"}, {"input": "P ∨ Q", "output": "P ∨ Q", "fidelity": 1.0, "ast_type": "LogicalDisjunction"}, {"input": "¬P", "output": "¬P", "fidelity": 1.0, "ast_type": "LogicalNegation"}, {"input": "¬(P ∨ Q)", "output": "¬(P ∨ Q)", "fidelity": 1.0, "ast_type": "LogicalNegation"}, {"input": "P ∧ Q ⇒ R", "output": "P ∧ Q ⇒ R", "fidelity": 1.0, "ast_type": "MaterialImplication"}, {"input": "A ∪ B", "output": "A ∪ B", "fidelity": 1.0, "ast_type": "BinaryOperation"}, {"input": "A ∩ B", "output": "A ∩ B", "fidelity": 1.0, "ast_type": "BinaryOperation"}, {"input": "x ∈ A", "output": "x ∈ A", "fidelity": 1.0, "ast_type": "BinaryOperation"}, {"input": "A ⊆ B", "output": "A ⊆ B", "fidelity": 1.0, "ast_type": "BinaryOperation"}, {"input": "x + y", "output": "x + y", "fidelity": 1.0, "ast_type": "BinaryOperation"}, {"input": "f(x)", "output": "f(x)", "fidelity": 1.0, "ast_type": "FunctionCall"}, {"input": "x = y", "output": "x = y", "fidelity": 1.0, "ast_type": "BinaryOperation"}, {"input": "∫ f(x) dx", "output": "∫ f(x) dx", "fidelity": 1.0, "ast_type": "Integral"}, {"input": "∑ xᵢ", "output": "∑ xᵢ", "fidelity": 1.0, "ast_type": "Summation"}, {"input": "∏ xᵢ", "output": "∏ xᵢ", "fidelity": 1.0, "ast_type": "Product"}, {"input": "∂f/∂x", "output": "∂f/∂x", "fidelity": 1.0, "ast_type": "PartialDerivative"}, {"input": "(P ∨ Q)", "output": "(P ∨ Q)", "fidelity": 1.0, "ast_type": "GroupedExpression"}, {"input": "f(x,y)", "output": "f(x,y)", "fidelity": 1.0, "ast_type": "FunctionCall"}, {"input": "x ≠ y", "output": "x ≠ y", "fidelity": 1.0, "ast_type": "BinaryOperation"}, {"input": "¬(G ∧ Q)", "output": "¬(G ∧ Q)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true}, {"input": "H ∨ F", "output": "H ∨ F", "fidelity": 1.0, "ast_type": "LogicalDisjunction", "category": "logical_reasoning", "generated": true}, {"input": "∀c: T(c)", "output": "∀c: T(c)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true}, {"input": "∃m ∀a: R(m,a)", "output": "∃m ∀a: R(m,a)", "fidelity": 1.0, "ast_type": "MixedQuantification", "category": "logical_reasoning", "generated": true}, {"input": "∃c: T(c)", "output": "∃c: T(c)", "fidelity": 1.0, "ast_type": "ExistentialQuantification", "category": "logical_reasoning", "generated": true}, {"input": "∀a ∀k: T(a,k)", "output": "∀a ∀k: T(a,k)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true}, {"input": "∀a ∈ 𝔹: Q(a)", "output": "∀a ∈ 𝔹: Q(a)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true}, {"input": "¬(S ∨ P)", "output": "¬(S ∨ P)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true}, {"input": "∀a ∈ ℚ: R(a)", "output": "∀a ∈ ℚ: R(a)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true}, {"input": "¬(Q ∧ G)", "output": "¬(Q ∧ G)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true}, {"input": "G ⇔ S", "output": "G ⇔ S", "fidelity": 1.0, "ast_type": "LogicalEquivalence", "category": "logical_reasoning", "generated": true}, {"input": "∀a: G(a)", "output": "∀a: G(a)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true}, {"input": "H ∨ G", "output": "H ∨ G", "fidelity": 1.0, "ast_type": "LogicalDisjunction", "category": "logical_reasoning", "generated": true}, {"input": "∃n: H(n)", "output": "∃n: H(n)", "fidelity": 1.0, "ast_type": "ExistentialQuantification", "category": "logical_reasoning", "generated": true}, {"input": "F ⇒ S", "output": "F ⇒ S", "fidelity": 1.0, "ast_type": "MaterialImplication", "category": "logical_reasoning", "generated": true}, {"input": "∀k: G(k)", "output": "∀k: G(k)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true}, {"input": "∀y ∀z: R(y,z)", "output": "∀y ∀z: R(y,z)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true}, {"input": "(P ∧ R) ⇒ P", "output": "(P ∧ R) ⇒ P", "fidelity": 1.0, "ast_type": "MaterialImplication", "category": "logical_reasoning", "generated": true}, {"input": "∃y: Q(y)", "output": "∃y: Q(y)", "fidelity": 1.0, "ast_type": "ExistentialQuantification", "category": "logical_reasoning", "generated": true}, {"input": "∀c ∈ 𝔹: T(c)", "output": "∀c ∈ 𝔹: T(c)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true}, {"input": "H ∧ Q ∧ P", "output": "H ∧ Q ∧ P", "fidelity": 1.0, "ast_type": "LogicalConjunction", "category": "logical_reasoning", "generated": true}, {"input": "G ∨ T", "output": "G ∨ T", "fidelity": 1.0, "ast_type": "LogicalDisjunction", "category": "logical_reasoning", "generated": true}, {"input": "∃b ∈ ℚ: S(b)", "output": "∃b ∈ ℚ: S(b)", "fidelity": 1.0, "ast_type": "ExistentialQuantification", "category": "logical_reasoning", "generated": true}, {"input": "∀z ∃b: S(z,b)", "output": "∀z ∃b: S(z,b)", "fidelity": 1.0, "ast_type": "MixedQuantification", "category": "logical_reasoning", "generated": true}, {"input": "S ∧ Q ∧ F", "output": "S ∧ Q ∧ F", "fidelity": 1.0, "ast_type": "LogicalConjunction", "category": "logical_reasoning", "generated": true}, {"input": "G(n) ⇒ S(n)", "output": "G(n) ⇒ S(n)", "fidelity": 1.0, "ast_type": "MaterialImplication", "category": "logical_reasoning", "generated": true}, {"input": "∀b ∀a: R(b,a)", "output": "∀b ∀a: R(b,a)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true}, {"input": "T ∨ G", "output": "T ∨ G", "fidelity": 1.0, "ast_type": "LogicalDisjunction", "category": "logical_reasoning", "generated": true}, {"input": "S ∧ Q", "output": "S ∧ Q", "fidelity": 1.0, "ast_type": "LogicalConjunction", "category": "logical_reasoning", "generated": true}, {"input": "(T ⇒ H) ∧ (H ⇒ T)", "output": "(T ⇒ H) ∧ (H ⇒ T)", "fidelity": 1.0, "ast_type": "LogicalEquivalence", "category": "logical_reasoning", "generated": true}, {"input": "F ∧ S", "output": "F ∧ S", "fidelity": 1.0, "ast_type": "LogicalConjunction", "category": "logical_reasoning", "generated": true}, {"input": "S ⇔ Q", "output": "S ⇔ Q", "fidelity": 1.0, "ast_type": "LogicalEquivalence", "category": "logical_reasoning", "generated": true}, {"input": "∀z: T(z)", "output": "∀z: T(z)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true}, {"input": "∃x: T(x)", "output": "∃x: T(x)", "fidelity": 1.0, "ast_type": "ExistentialQuantification", "category": "logical_reasoning", "generated": true}, {"input": "¬(H ∨ T)", "output": "¬(H ∨ T)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true}, {"input": "T ∨ H", "output": "T ∨ H", "fidelity": 1.0, "ast_type": "LogicalDisjunction", "category": "logical_reasoning", "generated": true}, {"input": "(R ∧ S) ⇒ Q", "output": "(R ∧ S) ⇒ Q", "fidelity": 1.0, "ast_type": "MaterialImplication", "category": "logical_reasoning", "generated": true}, {"input": "S(b) ⇒ R(b)", "output": "S(b) ⇒ R(b)", "fidelity": 1.0, "ast_type": "MaterialImplication", "category": "logical_reasoning", "generated": true}, {"input": "∃b: T(b)", "output": "∃b: T(b)", "fidelity": 1.0, "ast_type": "ExistentialQuantification", "category": "logical_reasoning", "generated": true}, {"input": "¬T", "output": "¬T", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true}, {"input": "∀y ∀n: T(y,n)", "output": "∀y ∀n: T(y,n)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true}, {"input": "¬(S ∨ R)", "output": "¬(S ∨ R)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true}, {"input": "F ∧ G", "output": "F ∧ G", "fidelity": 1.0, "ast_type": "LogicalConjunction", "category": "logical_reasoning", "generated": true}, {"input": "(F ∧ R) ⇒ H", "output": "(F ∧ R) ⇒ H", "fidelity": 1.0, "ast_type": "MaterialImplication", "category": "logical_reasoning", "generated": true}, {"input": "¬(R ∨ S)", "output": "¬(R ∨ S)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true}, {"input": "(G ⇒ P) ∧ (P ⇒ G)", "output": "(G ⇒ P) ∧ (P ⇒ G)", "fidelity": 1.0, "ast_type": "LogicalEquivalence", "category": "logical_reasoning", "generated": true}, {"input": "∀a ∃k: T(a,k)", "output": "∀a ∃k: T(a,k)", "fidelity": 1.0, "ast_type": "MixedQuantification", "category": "logical_reasoning", "generated": true}, {"input": "∃n ∀x: S(n,x)", "output": "∃n ∀x: S(n,x)", "fidelity": 1.0, "ast_type": "MixedQuantification", "category": "logical_reasoning", "generated": true}, {"input": "¬T", "output": "¬T", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true}, {"input": "¬(G ∨ R)", "output": "¬(G ∨ R)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true}, {"input": "T ∨ R", "output": "T ∨ R", "fidelity": 1.0, "ast_type": "LogicalDisjunction", "category": "logical_reasoning", "generated": true}, {"input": "∀a ∈ ℤ: T(a)", "output": "∀a ∈ ℤ: T(a)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true}, {"input": "¬F", "output": "¬F", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true}, {"input": "(T ⇒ S) ∧ (S ⇒ T)", "output": "(T ⇒ S) ∧ (S ⇒ T)", "fidelity": 1.0, "ast_type": "LogicalEquivalence", "category": "logical_reasoning", "generated": true}, {"input": "∀x ∃c: T(x,c)", "output": "∀x ∃c: T(x,c)", "fidelity": 1.0, "ast_type": "MixedQuantification", "category": "logical_reasoning", "generated": true}, {"input": "(F ∧ T) ⇒ H", "output": "(F ∧ T) ⇒ H", "fidelity": 1.0, "ast_type": "MaterialImplication", "category": "logical_reasoning", "generated": true}, {"input": "F ⇒ P", "output": "F ⇒ P", "fidelity": 1.0, "ast_type": "MaterialImplication", "category": "logical_reasoning", "generated": true}, {"input": "¬P", "output": "¬P", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true}, {"input": "P ⇒ F", "output": "P ⇒ F", "fidelity": 1.0, "ast_type": "MaterialImplication", "category": "logical_reasoning", "generated": true}, {"input": "∃a ∃b: S(a,b)", "output": "∃a ∃b: S(a,b)", "fidelity": 1.0, "ast_type": "ExistentialQuantification", "category": "logical_reasoning", "generated": true}, {"input": "Q ∨ H", "output": "Q ∨ H", "fidelity": 1.0, "ast_type": "LogicalDisjunction", "category": "logical_reasoning", "generated": true}, {"input": "R ∨ S", "output": "R ∨ S", "fidelity": 1.0, "ast_type": "LogicalDisjunction", "category": "logical_reasoning", "generated": true}, {"input": "(T ⇒ Q) ∧ (Q ⇒ T)", "output": "(T ⇒ Q) ∧ (Q ⇒ T)", "fidelity": 1.0, "ast_type": "LogicalEquivalence", "category": "logical_reasoning", "generated": true}, {"input": "∀b ∃x: T(b,x)", "output": "∀b ∃x: T(b,x)", "fidelity": 1.0, "ast_type": "MixedQuantification", "category": "logical_reasoning", "generated": true}, {"input": "(P ∧ Q) ⇒ T", "output": "(P ∧ Q) ⇒ T", "fidelity": 1.0, "ast_type": "MaterialImplication", "category": "logical_reasoning", "generated": true}, {"input": "Q ∨ F", "output": "Q ∨ F", "fidelity": 1.0, "ast_type": "LogicalDisjunction", "category": "logical_reasoning", "generated": true}, {"input": "G ⇔ R", "output": "G ⇔ R", "fidelity": 1.0, "ast_type": "LogicalEquivalence", "category": "logical_reasoning", "generated": true}, {"input": "F ⇒ G", "output": "F ⇒ G", "fidelity": 1.0, "ast_type": "MaterialImplication", "category": "logical_reasoning", "generated": true}, {"input": "(P ⇒ S) ∧ (S ⇒ P)", "output": "(P ⇒ S) ∧ (S ⇒ P)", "fidelity": 1.0, "ast_type": "LogicalEquivalence", "category": "logical_reasoning", "generated": true}, {"input": "(F ∧ T) ⇒ T", "output": "(F ∧ T) ⇒ T", "fidelity": 1.0, "ast_type": "MaterialImplication", "category": "logical_reasoning", "generated": true}, {"input": "¬(F ∨ R)", "output": "¬(F ∨ R)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true}, {"input": "(S ⇒ P) ∧ (P ⇒ S)", "output": "(S ⇒ P) ∧ (P ⇒ S)", "fidelity": 1.0, "ast_type": "LogicalEquivalence", "category": "logical_reasoning", "generated": true}, {"input": "F ⇒ Q", "output": "F ⇒ Q", "fidelity": 1.0, "ast_type": "MaterialImplication", "category": "logical_reasoning", "generated": true}, {"input": "∀k: Q(k)", "output": "∀k: Q(k)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true}, {"input": "H ∧ S ∧ Q", "output": "H ∧ S ∧ Q", "fidelity": 1.0, "ast_type": "LogicalConjunction", "category": "logical_reasoning", "generated": true}, {"input": "H ∧ Q ∧ F", "output": "H ∧ Q ∧ F", "fidelity": 1.0, "ast_type": "LogicalConjunction", "category": "logical_reasoning", "generated": true}, {"input": "P ⇒ H", "output": "P ⇒ H", "fidelity": 1.0, "ast_type": "MaterialImplication", "category": "logical_reasoning", "generated": true}, {"input": "¬(P ∨ G)", "output": "¬(P ∨ G)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true}, {"input": "X ⊃ B", "output": "X ⊃ B", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "Z ∩ X", "output": "Z ∩ X", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "∃y ∈ D: y ∉ Z", "output": "∃y ∈ D: y ∉ Z", "fidelity": 1.0, "ast_type": "ExistentialQuantification", "category": "set_theory", "generated": true}, {"input": "Y ∪ B", "output": "Y ∪ B", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "Z \\ Y", "output": "Z \\ Y", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "Z ∩ D", "output": "Z ∩ D", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "m ∉ Y", "output": "m ∉ Y", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "∀a ∈ A: a ∈ C", "output": "∀a ∈ A: a ∈ C", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "set_theory", "generated": true}, {"input": "(A ∩ D) ⊆ Y", "output": "(A ∩ D) ⊆ Y", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "X ∩ C", "output": "X ∩ C", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "n ∉ Z", "output": "n ∉ Z", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "∀z ∈ X: z ∈ A", "output": "∀z ∈ X: z ∈ A", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "set_theory", "generated": true}, {"input": "Z ∩ (B ∪ D)", "output": "Z ∩ (B ∪ D)", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "X ∩ B", "output": "X ∩ B", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "z ∉ X", "output": "z ∉ X", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "D ⊂ B", "output": "D ⊂ B", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "C ⊇ B", "output": "C ⊇ B", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "a ∉ B", "output": "a ∉ B", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "C ⊇ B", "output": "C ⊇ B", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "∃n ∈ A: n ∉ D", "output": "∃n ∈ A: n ∉ D", "fidelity": 1.0, "ast_type": "ExistentialQuantification", "category": "set_theory", "generated": true}, {"input": "b ∉ Z", "output": "b ∉ Z", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "X ∪ D", "output": "X ∪ D", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "x ∈ B", "output": "x ∈ B", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "Y ∩ C", "output": "Y ∩ C", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "Y ∩ (B ∪ B)", "output": "Y ∩ (B ∪ B)", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "C \\ A", "output": "C \\ A", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "∃c ∈ D: c ∉ A", "output": "∃c ∈ D: c ∉ A", "fidelity": 1.0, "ast_type": "ExistentialQuantification", "category": "set_theory", "generated": true}, {"input": "Y ⊇ D", "output": "Y ⊇ D", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "∃x ∈ B: x ∉ C", "output": "∃x ∈ B: x ∉ C", "fidelity": 1.0, "ast_type": "ExistentialQuantification", "category": "set_theory", "generated": true}, {"input": "(Y ∩ Z) ⊆ B", "output": "(Y ∩ Z) ⊆ B", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "Y ∩ Z", "output": "Y ∩ Z", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "(B ∩ Y) ⊆ Y", "output": "(B ∩ Y) ⊆ Y", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "C \\ X", "output": "C \\ X", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "A ⊃ C", "output": "A ⊃ C", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "Y ⊆ B", "output": "Y ⊆ B", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "Y ⊇ Z", "output": "Y ⊇ Z", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "X ∪ D", "output": "X ∪ D", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "B ∩ A", "output": "B ∩ A", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "Y \\ Z", "output": "Y \\ Z", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "A ⊂ Z", "output": "A ⊂ Z", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "A ∩ X", "output": "A ∩ X", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "∀m ∈ Z: m ∈ A", "output": "∀m ∈ Z: m ∈ A", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "set_theory", "generated": true}, {"input": "(X ∪ B) ∩ A", "output": "(X ∪ B) ∩ A", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "(B ∪ C) ∩ D", "output": "(B ∪ C) ∩ D", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "∃c ∈ A: c ∉ X", "output": "∃c ∈ A: c ∉ X", "fidelity": 1.0, "ast_type": "ExistentialQuantification", "category": "set_theory", "generated": true}, {"input": "Z ∪ B", "output": "Z ∪ B", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "c ∈ X", "output": "c ∈ X", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "D \\ B", "output": "D \\ B", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "D ⊇ X", "output": "D ⊇ X", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "x ∈ B", "output": "x ∈ B", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "Z ∩ (Y ∪ A)", "output": "Z ∩ (Y ∪ A)", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "C ⊆ B", "output": "C ⊆ B", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "C ⊆ Y", "output": "C ⊆ Y", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "∃x ∈ X: x ∉ Y", "output": "∃x ∈ X: x ∉ Y", "fidelity": 1.0, "ast_type": "ExistentialQuantification", "category": "set_theory", "generated": true}, {"input": "Z \\ Y", "output": "Z \\ Y", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "B ∩ (Y ∪ C)", "output": "B ∩ (Y ∪ C)", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "set_theory", "generated": true}, {"input": "a ≤ z", "output": "a ≤ z", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "f(x)", "output": "f(x)", "fidelity": 1.0, "ast_type": "FunctionCall", "category": "mathematical_reasoning", "generated": true}, {"input": "x ≥ y", "output": "x ≥ y", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "z = b", "output": "z = b", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "ψ(y)", "output": "ψ(y)", "fidelity": 1.0, "ast_type": "FunctionCall", "category": "mathematical_reasoning", "generated": true}, {"input": "m ≤ y", "output": "m ≤ y", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "∃b ∈ ℂ: h(b) = x", "output": "∃b ∈ ℂ: h(b) = x", "fidelity": 1.0, "ast_type": "ExistentialQuantification", "category": "mathematical_reasoning", "generated": true}, {"input": "a < n", "output": "a < n", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "b ≤ m", "output": "b ≤ m", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "x² + c² = a²", "output": "x² + c² = a²", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "a = x", "output": "a = x", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "φ(z)", "output": "φ(z)", "fidelity": 1.0, "ast_type": "FunctionCall", "category": "mathematical_reasoning", "generated": true}, {"input": "f(x,n)", "output": "f(x,n)", "fidelity": 1.0, "ast_type": "FunctionCall", "category": "mathematical_reasoning", "generated": true}, {"input": "b · z = n", "output": "b · z = n", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "χ(y,x)", "output": "χ(y,x)", "fidelity": 1.0, "ast_type": "FunctionCall", "category": "mathematical_reasoning", "generated": true}, {"input": "χ(y,b)", "output": "χ(y,b)", "fidelity": 1.0, "ast_type": "FunctionCall", "category": "mathematical_reasoning", "generated": true}, {"input": "∃y ∈ ℤ: h(y) = x", "output": "∃y ∈ ℤ: h(y) = x", "fidelity": 1.0, "ast_type": "ExistentialQuantification", "category": "mathematical_reasoning", "generated": true}, {"input": "n ≥ c", "output": "n ≥ c", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "m ≤ x", "output": "m ≤ x", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "m < b", "output": "m < b", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "x ≤ a", "output": "x ≤ a", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "φ(z,y)", "output": "φ(z,y)", "fidelity": 1.0, "ast_type": "FunctionCall", "category": "mathematical_reasoning", "generated": true}, {"input": "a ≥ y", "output": "a ≥ y", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "∃c ∈ ℂ: χ(c) = m", "output": "∃c ∈ ℂ: χ(c) = m", "fidelity": 1.0, "ast_type": "ExistentialQuantification", "category": "mathematical_reasoning", "generated": true}, {"input": "c + x = z", "output": "c + x = z", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "∀n ∈ 𝔹: g(n) ∈ ℤ", "output": "∀n ∈ 𝔹: g(n) ∈ ℤ", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "mathematical_reasoning", "generated": true}, {"input": "ψ(z)", "output": "ψ(z)", "fidelity": 1.0, "ast_type": "FunctionCall", "category": "mathematical_reasoning", "generated": true}, {"input": "z ≥ b", "output": "z ≥ b", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "n = a", "output": "n = a", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "∃z ∈ ℚ: φ(z) = b", "output": "∃z ∈ ℚ: φ(z) = b", "fidelity": 1.0, "ast_type": "ExistentialQuantification", "category": "mathematical_reasoning", "generated": true}, {"input": "h(y) = m", "output": "h(y) = m", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "z = k", "output": "z = k", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "b ≠ a", "output": "b ≠ a", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "y ≠ k", "output": "y ≠ k", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "z ≠ x", "output": "z ≠ x", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "k · m = a", "output": "k · m = a", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "b = x", "output": "b = x", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "∀k ∈ ℂ: φ(k) ∈ ℝ", "output": "∀k ∈ ℂ: φ(k) ∈ ℝ", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "mathematical_reasoning", "generated": true}, {"input": "g(a,c)", "output": "g(a,c)", "fidelity": 1.0, "ast_type": "FunctionCall", "category": "mathematical_reasoning", "generated": true}, {"input": "k ≠ c", "output": "k ≠ c", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "∃a ∈ ℤ: h(a) = y", "output": "∃a ∈ ℤ: h(a) = y", "fidelity": 1.0, "ast_type": "ExistentialQuantification", "category": "mathematical_reasoning", "generated": true}, {"input": "χ(z)", "output": "χ(z)", "fidelity": 1.0, "ast_type": "FunctionCall", "category": "mathematical_reasoning", "generated": true}, {"input": "c ≥ y", "output": "c ≥ y", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "z > b", "output": "z > b", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "a² + z² = c²", "output": "a² + z² = c²", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "ψ(a)", "output": "ψ(a)", "fidelity": 1.0, "ast_type": "FunctionCall", "category": "mathematical_reasoning", "generated": true}, {"input": "x = y", "output": "x = y", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "c ≥ m", "output": "c ≥ m", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "k ≥ n", "output": "k ≥ n", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "ψ(m)", "output": "ψ(m)", "fidelity": 1.0, "ast_type": "FunctionCall", "category": "mathematical_reasoning", "generated": true}, {"input": "∀m ∈ ℝ: h(m) ∈ ℝ", "output": "∀m ∈ ℝ: h(m) ∈ ℝ", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "mathematical_reasoning", "generated": true}, {"input": "χ(m) = k", "output": "χ(m) = k", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "f(a,z)", "output": "f(a,z)", "fidelity": 1.0, "ast_type": "FunctionCall", "category": "mathematical_reasoning", "generated": true}, {"input": "b ≠ m", "output": "b ≠ m", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "ψ(m) = k", "output": "ψ(m) = k", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "x ≥ k", "output": "x ≥ k", "fidelity": 1.0, "ast_type": "BinaryOperation", "category": "mathematical_reasoning", "generated": true}, {"input": "¬(∃a: Q(a)) ⇔ (∀a: ¬Q(a))", "output": "¬(∃a: Q(a)) ⇔ (∀a: ¬Q(a))", "fidelity": 1.0, "ast_type": "ComplexLogical", "category": "complex_reasoning", "generated": true}, {"input": "∀b ∈ ℕ: ∃a ∈ 𝔹: φ(b) = a", "output": "∀b ∈ ℕ: ∃a ∈ 𝔹: φ(b) = a", "fidelity": 1.0, "ast_type": "ComplexMath", "category": "complex_reasoning", "generated": true}, {"input": "∀c ∈ ℚ: ∃x ∈ ℂ: ψ(c) = x", "output": "∀c ∈ ℚ: ∃x ∈ ℂ: ψ(c) = x", "fidelity": 1.0, "ast_type": "ComplexMath", "category": "complex_reasoning", "generated": true}, {"input": "∀n ∈ ℂ: ∃z ∈ 𝔹: ψ(n) = z", "output": "∀n ∈ ℂ: ∃z ∈ 𝔹: ψ(n) = z", "fidelity": 1.0, "ast_type": "ComplexMath", "category": "complex_reasoning", "generated": true}, {"input": "(m ∈ A) ∧ (χ(m) ∈ B)", "output": "(m ∈ A) ∧ (χ(m) ∈ B)", "fidelity": 1.0, "ast_type": "ComplexMath", "category": "complex_reasoning", "generated": true}, {"input": "∀c ∈ ℝ: ∃x ∈ ℤ: g(c) = x", "output": "∀c ∈ ℝ: ∃x ∈ ℤ: g(c) = x", "fidelity": 1.0, "ast_type": "ComplexMath", "category": "complex_reasoning", "generated": true}, {"input": "(A ∩ D) ⊆ (A ∪ Y)", "output": "(A ∩ D) ⊆ (A ∪ Y)", "fidelity": 1.0, "ast_type": "ComplexSet", "category": "complex_reasoning", "generated": true}, {"input": "∀a ∈ ℕ: ∃m ∈ ℕ: g(a) = m", "output": "∀a ∈ ℕ: ∃m ∈ ℕ: g(a) = m", "fidelity": 1.0, "ast_type": "ComplexMath", "category": "complex_reasoning", "generated": true}, {"input": "∀k ∈ ℕ: ∃c ∈ ℕ: g(k) = c", "output": "∀k ∈ ℕ: ∃c ∈ ℕ: g(k) = c", "fidelity": 1.0, "ast_type": "ComplexMath", "category": "complex_reasoning", "generated": true}, {"input": "(D ∩ Z) ⊆ (C ∪ C)", "output": "(D ∩ Z) ⊆ (C ∪ C)", "fidelity": 1.0, "ast_type": "ComplexSet", "category": "complex_reasoning", "generated": true}, {"input": "∀k ∈ ℕ: ∃n ∈ 𝔹: g(k) = n", "output": "∀k ∈ ℕ: ∃n ∈ 𝔹: g(k) = n", "fidelity": 1.0, "ast_type": "ComplexMath", "category": "complex_reasoning", "generated": true}, {"input": "(n ∈ C) ∧ (φ(n) ∈ B)", "output": "(n ∈ C) ∧ (φ(n) ∈ B)", "fidelity": 1.0, "ast_type": "ComplexMath", "category": "complex_reasoning", "generated": true}, {"input": "(∀a: R(a)) ⇒ (∃c: G(c))", "output": "(∀a: R(a)) ⇒ (∃c: G(c))", "fidelity": 1.0, "ast_type": "ComplexLogical", "category": "complex_reasoning", "generated": true}, {"input": "(Z ∪ X) ∩ (A ∪ B)", "output": "(Z ∪ X) ∩ (A ∪ B)", "fidelity": 1.0, "ast_type": "ComplexSet", "category": "complex_reasoning", "generated": true}, {"input": "(G ∧ S) ∨ (F ∧ Q)", "output": "(G ∧ S) ∨ (F ∧ Q)", "fidelity": 1.0, "ast_type": "ComplexLogical", "category": "complex_reasoning", "generated": true}, {"input": "(X ∩ Z) ⊆ (A ∪ Y)", "output": "(X ∩ Z) ⊆ (A ∪ Y)", "fidelity": 1.0, "ast_type": "ComplexSet", "category": "complex_reasoning", "generated": true}, {"input": "∀a ∈ ℤ: ∃k ∈ ℚ: ψ(a) = k", "output": "∀a ∈ ℤ: ∃k ∈ ℚ: ψ(a) = k", "fidelity": 1.0, "ast_type": "ComplexMath", "category": "complex_reasoning", "generated": true}, {"input": "(∀x: H(x)) ⇒ (∃y: P(y))", "output": "(∀x: H(x)) ⇒ (∃y: P(y))", "fidelity": 1.0, "ast_type": "ComplexLogical", "category": "complex_reasoning", "generated": true}, {"input": "(B ∩ D) ⊆ (B ∪ A)", "output": "(B ∩ D) ⊆ (B ∪ A)", "fidelity": 1.0, "ast_type": "ComplexSet", "category": "complex_reasoning", "generated": true}, {"input": "(m ∈ A) ∧ (h(m) ∈ X)", "output": "(m ∈ A) ∧ (h(m) ∈ X)", "fidelity": 1.0, "ast_type": "ComplexMath", "category": "complex_reasoning", "generated": true}, {"input": "(b ∈ B) ∧ (f(b) ∈ C)", "output": "(b ∈ B) ∧ (f(b) ∈ C)", "fidelity": 1.0, "ast_type": "ComplexMath", "category": "complex_reasoning", "generated": true}, {"input": "∀y ∈ ℕ: ∃b ∈ ℂ: h(y) = b", "output": "∀y ∈ ℕ: ∃b ∈ ℂ: h(y) = b", "fidelity": 1.0, "ast_type": "ComplexMath", "category": "complex_reasoning", "generated": true}, {"input": "(Z ∩ B) ⊆ (Y ∪ Y)", "output": "(Z ∩ B) ⊆ (Y ∪ Y)", "fidelity": 1.0, "ast_type": "ComplexSet", "category": "complex_reasoning", "generated": true}, {"input": "∀c ∈ ℤ: ∃k ∈ ℚ: φ(c) = k", "output": "∀c ∈ ℤ: ∃k ∈ ℚ: φ(c) = k", "fidelity": 1.0, "ast_type": "ComplexMath", "category": "complex_reasoning", "generated": true}, {"input": "(B ∩ A) ⊆ (D ∪ A)", "output": "(B ∩ A) ⊆ (D ∪ A)", "fidelity": 1.0, "ast_type": "ComplexSet", "category": "complex_reasoning", "generated": true}, {"input": "(D ∪ X) ∩ (C ∪ Z)", "output": "(D ∪ X) ∩ (C ∪ Z)", "fidelity": 1.0, "ast_type": "ComplexSet", "category": "complex_reasoning", "generated": true}, {"input": "(∀y: S(y)) ⇒ (∃x: Q(x))", "output": "(∀y: S(y)) ⇒ (∃x: Q(x))", "fidelity": 1.0, "ast_type": "ComplexLogical", "category": "complex_reasoning", "generated": true}, {"input": "¬(∃x: G(x)) ⇔ (∀x: ¬G(x))", "output": "¬(∃x: G(x)) ⇔ (∀x: ¬G(x))", "fidelity": 1.0, "ast_type": "ComplexLogical", "category": "complex_reasoning", "generated": true}, {"input": "(X ∪ Z) ∩ (B ∪ Y)", "output": "(X ∪ Z) ∩ (B ∪ Y)", "fidelity": 1.0, "ast_type": "ComplexSet", "category": "complex_reasoning", "generated": true}, {"input": "∀a ∈ 𝔹: ∃z ∈ ℕ: g(a) = z", "output": "∀a ∈ 𝔹: ∃z ∈ ℕ: g(a) = z", "fidelity": 1.0, "ast_type": "ComplexMath", "category": "complex_reasoning", "generated": true}, {"input": "¬(∃k: Q(k)) ⇔ (∀k: ¬Q(k))", "output": "¬(∃k: Q(k)) ⇔ (∀k: ¬Q(k))", "fidelity": 1.0, "ast_type": "ComplexLogical", "category": "complex_reasoning", "generated": true}, {"input": "(Y ∩ C) ⊆ (X ∪ D)", "output": "(Y ∩ C) ⊆ (X ∪ D)", "fidelity": 1.0, "ast_type": "ComplexSet", "category": "complex_reasoning", "generated": true}, {"input": "(∀x: S(x)) ⇒ (∃a: T(a))", "output": "(∀x: S(x)) ⇒ (∃a: T(a))", "fidelity": 1.0, "ast_type": "ComplexLogical", "category": "complex_reasoning", "generated": true}, {"input": "(C ∪ B) ∩ (C ∪ D)", "output": "(C ∪ B) ∩ (C ∪ D)", "fidelity": 1.0, "ast_type": "ComplexSet", "category": "complex_reasoning", "generated": true}, {"input": "(X ∪ Z) ∩ (Z ∪ Y)", "output": "(X ∪ Z) ∩ (Z ∪ Y)", "fidelity": 1.0, "ast_type": "ComplexSet", "category": "complex_reasoning", "generated": true}, {"input": "H ⇒ S", "output": "H ⇒ S", "fidelity": 1.0, "ast_type": "MaterialImplication", "category": "logical_reasoning", "generated": true, "complexity": 3}, {"input": "∀b ∃a: Greater(b,a)", "output": "∀b ∃a: Greater(b,a)", "fidelity": 1.0, "ast_type": "MixedQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∀m ∈ ℝ: R(m)", "output": "∀m ∈ ℝ: R(m)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∃m ∈ 𝔹: Q(m)", "output": "∃m ∈ 𝔹: Q(m)", "fidelity": 1.0, "ast_type": "ExistentialQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "¬(G ∧ Q)", "output": "¬(G ∧ Q)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true, "complexity": 5}, {"input": "(∀k: F(k)) ⇒ (∃k: H(k))", "output": "(∀k: F(k)) ⇒ (∃k: H(k))", "fidelity": 1.0, "ast_type": "ComplexImplication", "category": "logical_reasoning", "generated": true, "complexity": 15}, {"input": "¬(P ∨ G)", "output": "¬(P ∨ G)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true, "complexity": 5}, {"input": "F ∨ Q ∨ S", "output": "F ∨ Q ∨ S", "fidelity": 1.0, "ast_type": "LogicalDisjunction", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∀k ∈ ℚ: P(k)", "output": "∀k ∈ ℚ: P(k)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∀a ∃y: Less(a,y)", "output": "∀a ∃y: Less(a,y)", "fidelity": 1.0, "ast_type": "MixedQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "(∀a: P(a)) ⇒ (∃a: T(a))", "output": "(∀a: P(a)) ⇒ (∃a: T(a))", "fidelity": 1.0, "ast_type": "ComplexImplication", "category": "logical_reasoning", "generated": true, "complexity": 15}, {"input": "∀c ∀y: (Less(c,y) ⇒ Greater(c,y))", "output": "∀c ∀y: (Less(c,y) ⇒ Greater(c,y))", "fidelity": 1.0, "ast_type": "UniversalImplication", "category": "logical_reasoning", "generated": true, "complexity": 13}, {"input": "∀m ∈ 𝔹: Q(m)", "output": "∀m ∈ 𝔹: Q(m)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "G ⇒ G", "output": "G ⇒ G", "fidelity": 1.0, "ast_type": "MaterialImplication", "category": "logical_reasoning", "generated": true, "complexity": 3}, {"input": "(P ∧ Q) ⇒ R", "output": "(P ∧ Q) ⇒ R", "fidelity": 1.0, "ast_type": "MaterialImplication", "category": "logical_reasoning", "generated": true, "complexity": 8}, {"input": "G ∨ G ∨ T", "output": "G ∨ G ∨ T", "fidelity": 1.0, "ast_type": "LogicalDisjunction", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∀b ∈ 𝔹: R(b)", "output": "∀b ∈ 𝔹: R(b)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∀c ∈ ℕ: P(c)", "output": "∀c ∈ ℕ: P(c)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "(R ∧ P) ⇒ Q", "output": "(R ∧ P) ⇒ Q", "fidelity": 1.0, "ast_type": "MaterialImplication", "category": "logical_reasoning", "generated": true, "complexity": 8}, {"input": "T ⇔ P", "output": "T ⇔ P", "fidelity": 1.0, "ast_type": "LogicalEquivalence", "category": "logical_reasoning", "generated": true, "complexity": 3}, {"input": "T ⇒ S", "output": "T ⇒ S", "fidelity": 1.0, "ast_type": "MaterialImplication", "category": "logical_reasoning", "generated": true, "complexity": 3}, {"input": "(G ∧ S) ⇒ Q", "output": "(G ∧ S) ⇒ Q", "fidelity": 1.0, "ast_type": "MaterialImplication", "category": "logical_reasoning", "generated": true, "complexity": 8}, {"input": "(∀m: H(m)) ⇒ (∃m: R(m))", "output": "(∀m: H(m)) ⇒ (∃m: R(m))", "fidelity": 1.0, "ast_type": "ComplexImplication", "category": "logical_reasoning", "generated": true, "complexity": 15}, {"input": "∀x ∀a: (Equal(x,a) ⇒ T(x,a))", "output": "∀x ∀a: (Equal(x,a) ⇒ T(x,a))", "fidelity": 1.0, "ast_type": "UniversalImplication", "category": "logical_reasoning", "generated": true, "complexity": 13}, {"input": "H ∧ P ∧ Q", "output": "H ∧ P ∧ Q", "fidelity": 1.0, "ast_type": "LogicalConjunction", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∀b ∃y: S(b,y)", "output": "∀b ∃y: S(b,y)", "fidelity": 1.0, "ast_type": "MixedQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "¬(G ∨ Q)", "output": "¬(G ∨ Q)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true, "complexity": 5}, {"input": "(∀a: Q(a)) ⇒ (∃a: R(a))", "output": "(∀a: Q(a)) ⇒ (∃a: R(a))", "fidelity": 1.0, "ast_type": "ComplexImplication", "category": "logical_reasoning", "generated": true, "complexity": 15}, {"input": "Q ⇔ G", "output": "Q ⇔ G", "fidelity": 1.0, "ast_type": "LogicalEquivalence", "category": "logical_reasoning", "generated": true, "complexity": 3}, {"input": "F ∨ R ∨ T", "output": "F ∨ R ∨ T", "fidelity": 1.0, "ast_type": "LogicalDisjunction", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "(∀a: S(a)) ⇒ (∃a: S(a))", "output": "(∀a: S(a)) ⇒ (∃a: S(a))", "fidelity": 1.0, "ast_type": "ComplexImplication", "category": "logical_reasoning", "generated": true, "complexity": 15}, {"input": "∃m ∈ ℤ: P(m)", "output": "∃m ∈ ℤ: P(m)", "fidelity": 1.0, "ast_type": "ExistentialQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∀c ∈ 𝔹: Q(c)", "output": "∀c ∈ 𝔹: Q(c)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "¬(P ∧ R)", "output": "¬(P ∧ R)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true, "complexity": 5}, {"input": "¬(G ∨ R)", "output": "¬(G ∨ R)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true, "complexity": 5}, {"input": "∀z ∃n: Greater(z,n)", "output": "∀z ∃n: Greater(z,n)", "fidelity": 1.0, "ast_type": "MixedQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "S ∨ H ∨ R", "output": "S ∨ H ∨ R", "fidelity": 1.0, "ast_type": "LogicalDisjunction", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∀a ∈ 𝔹: F(a)", "output": "∀a ∈ 𝔹: F(a)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "T ⇔ F", "output": "T ⇔ F", "fidelity": 1.0, "ast_type": "LogicalEquivalence", "category": "logical_reasoning", "generated": true, "complexity": 3}, {"input": "∀z ∈ ℤ: H(z)", "output": "∀z ∈ ℤ: H(z)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∀k ∀k: (T(k,k) ⇒ Greater(k,k))", "output": "∀k ∀k: (T(k,k) ⇒ Greater(k,k))", "fidelity": 1.0, "ast_type": "UniversalImplication", "category": "logical_reasoning", "generated": true, "complexity": 13}, {"input": "∃k ∈ ℕ: S(k)", "output": "∃k ∈ ℕ: S(k)", "fidelity": 1.0, "ast_type": "ExistentialQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∀b ∈ ℤ: T(b)", "output": "∀b ∈ ℤ: T(b)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "S ⇔ G", "output": "S ⇔ G", "fidelity": 1.0, "ast_type": "LogicalEquivalence", "category": "logical_reasoning", "generated": true, "complexity": 3}, {"input": "G ⇔ F", "output": "G ⇔ F", "fidelity": 1.0, "ast_type": "LogicalEquivalence", "category": "logical_reasoning", "generated": true, "complexity": 3}, {"input": "¬(F ∧ S)", "output": "¬(F ∧ S)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true, "complexity": 5}, {"input": "G ∨ T ∨ T", "output": "G ∨ T ∨ T", "fidelity": 1.0, "ast_type": "LogicalDisjunction", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∀n ∈ ℚ: R(n)", "output": "∀n ∈ ℚ: R(n)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "G ⇔ F", "output": "G ⇔ F", "fidelity": 1.0, "ast_type": "LogicalEquivalence", "category": "logical_reasoning", "generated": true, "complexity": 3}, {"input": "(Q ∧ S) ⇒ R", "output": "(Q ∧ S) ⇒ R", "fidelity": 1.0, "ast_type": "MaterialImplication", "category": "logical_reasoning", "generated": true, "complexity": 8}, {"input": "¬(P ∧ G)", "output": "¬(P ∧ G)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true, "complexity": 5}, {"input": "Q ∨ S ∨ R", "output": "Q ∨ S ∨ R", "fidelity": 1.0, "ast_type": "LogicalDisjunction", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∀c ∃m: Equal(c,m)", "output": "∀c ∃m: Equal(c,m)", "fidelity": 1.0, "ast_type": "MixedQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "P ∨ G ∨ H", "output": "P ∨ G ∨ H", "fidelity": 1.0, "ast_type": "LogicalDisjunction", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "H ⇔ P", "output": "H ⇔ P", "fidelity": 1.0, "ast_type": "LogicalEquivalence", "category": "logical_reasoning", "generated": true, "complexity": 3}, {"input": "F ∧ F ∧ G", "output": "F ∧ F ∧ G", "fidelity": 1.0, "ast_type": "LogicalConjunction", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "(∀a: H(a)) ⇒ (∃a: H(a))", "output": "(∀a: H(a)) ⇒ (∃a: H(a))", "fidelity": 1.0, "ast_type": "ComplexImplication", "category": "logical_reasoning", "generated": true, "complexity": 15}, {"input": "∃n ∈ ℕ: G(n)", "output": "∃n ∈ ℕ: G(n)", "fidelity": 1.0, "ast_type": "ExistentialQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "¬(P ∨ G)", "output": "¬(P ∨ G)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true, "complexity": 5}, {"input": "∀m ∀z: (Less(m,z) ⇒ S(m,z))", "output": "∀m ∀z: (Less(m,z) ⇒ S(m,z))", "fidelity": 1.0, "ast_type": "UniversalImplication", "category": "logical_reasoning", "generated": true, "complexity": 13}, {"input": "∀k ∃k: R(k,k)", "output": "∀k ∃k: R(k,k)", "fidelity": 1.0, "ast_type": "MixedQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "(∀z: F(z)) ⇒ (∃z: P(z))", "output": "(∀z: F(z)) ⇒ (∃z: P(z))", "fidelity": 1.0, "ast_type": "ComplexImplication", "category": "logical_reasoning", "generated": true, "complexity": 15}, {"input": "Q ⇒ T", "output": "Q ⇒ T", "fidelity": 1.0, "ast_type": "MaterialImplication", "category": "logical_reasoning", "generated": true, "complexity": 3}, {"input": "T ⇒ G", "output": "T ⇒ G", "fidelity": 1.0, "ast_type": "MaterialImplication", "category": "logical_reasoning", "generated": true, "complexity": 3}, {"input": "(F ∧ G) ⇒ S", "output": "(F ∧ G) ⇒ S", "fidelity": 1.0, "ast_type": "MaterialImplication", "category": "logical_reasoning", "generated": true, "complexity": 8}, {"input": "∀c ∈ ℚ: H(c)", "output": "∀c ∈ ℚ: H(c)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "P ∧ P ∧ R", "output": "P ∧ P ∧ R", "fidelity": 1.0, "ast_type": "LogicalConjunction", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "P ⇒ S", "output": "P ⇒ S", "fidelity": 1.0, "ast_type": "MaterialImplication", "category": "logical_reasoning", "generated": true, "complexity": 3}, {"input": "R ⇔ R", "output": "R ⇔ R", "fidelity": 1.0, "ast_type": "LogicalEquivalence", "category": "logical_reasoning", "generated": true, "complexity": 3}, {"input": "∀n ∃z: Equal(n,z)", "output": "∀n ∃z: Equal(n,z)", "fidelity": 1.0, "ast_type": "MixedQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "R ∨ R ∨ S", "output": "R ∨ R ∨ S", "fidelity": 1.0, "ast_type": "LogicalDisjunction", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∀n ∀m: (R(n,m) ⇒ Greater(n,m))", "output": "∀n ∀m: (R(n,m) ⇒ Greater(n,m))", "fidelity": 1.0, "ast_type": "UniversalImplication", "category": "logical_reasoning", "generated": true, "complexity": 13}, {"input": "|B|", "output": "|B|", "fidelity": 1.0, "ast_type": "SetCardinality", "category": "set_theory", "generated": true, "complexity": 0}, {"input": "b ∉ Y", "output": "b ∉ Y", "fidelity": 1.0, "ast_type": "SetNonMembership", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "y ∉ Y", "output": "y ∉ Y", "fidelity": 1.0, "ast_type": "SetNonMembership", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "∅", "output": "∅", "fidelity": 1.0, "ast_type": "EmptySet", "category": "set_theory", "generated": true, "complexity": 0}, {"input": "C ∩ A", "output": "C ∩ A", "fidelity": 1.0, "ast_type": "SetIntersection", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "b ∉ X", "output": "b ∉ X", "fidelity": 1.0, "ast_type": "SetNonMembership", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "z ∉ Z", "output": "z ∉ Z", "fidelity": 1.0, "ast_type": "SetNonMembership", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "X ∩ X", "output": "X ∩ X", "fidelity": 1.0, "ast_type": "SetIntersection", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "𝒫(T)", "output": "𝒫(T)", "fidelity": 1.0, "ast_type": "PowerSet", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "B ∩ T", "output": "B ∩ T", "fidelity": 1.0, "ast_type": "SetIntersection", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "A ⊆ X", "output": "A ⊆ X", "fidelity": 1.0, "ast_type": "SetSubset", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "∅", "output": "∅", "fidelity": 1.0, "ast_type": "EmptySet", "category": "set_theory", "generated": true, "complexity": 0}, {"input": "|Y|", "output": "|Y|", "fidelity": 1.0, "ast_type": "SetCardinality", "category": "set_theory", "generated": true, "complexity": 0}, {"input": "|X|", "output": "|X|", "fidelity": 1.0, "ast_type": "SetCardinality", "category": "set_theory", "generated": true, "complexity": 0}, {"input": "b ∈ Z", "output": "b ∈ Z", "fidelity": 1.0, "ast_type": "SetMembership", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "X ∪ S", "output": "X ∪ S", "fidelity": 1.0, "ast_type": "SetUnion", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "a ∈ Z", "output": "a ∈ Z", "fidelity": 1.0, "ast_type": "SetMembership", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "b ∈ C", "output": "b ∈ C", "fidelity": 1.0, "ast_type": "SetMembership", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "A ∩ X", "output": "A ∩ X", "fidelity": 1.0, "ast_type": "SetIntersection", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "T \\ Z", "output": "T \\ Z", "fidelity": 1.0, "ast_type": "SetDifference", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "S \\ A", "output": "S \\ A", "fidelity": 1.0, "ast_type": "SetDifference", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "a ∉ Y", "output": "a ∉ Y", "fidelity": 1.0, "ast_type": "SetNonMembership", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "T \\ T", "output": "T \\ T", "fidelity": 1.0, "ast_type": "SetDifference", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "X ⊇ C", "output": "X ⊇ C", "fidelity": 1.0, "ast_type": "SetSuperset", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "𝒫(A)", "output": "𝒫(A)", "fidelity": 1.0, "ast_type": "PowerSet", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "Z ∩ B", "output": "Z ∩ B", "fidelity": 1.0, "ast_type": "SetIntersection", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "|B|", "output": "|B|", "fidelity": 1.0, "ast_type": "SetCardinality", "category": "set_theory", "generated": true, "complexity": 0}, {"input": "𝒫(B)", "output": "𝒫(B)", "fidelity": 1.0, "ast_type": "PowerSet", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "y ∉ C", "output": "y ∉ C", "fidelity": 1.0, "ast_type": "SetNonMembership", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "𝒫(S)", "output": "𝒫(S)", "fidelity": 1.0, "ast_type": "PowerSet", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "B ⊇ B", "output": "B ⊇ B", "fidelity": 1.0, "ast_type": "SetSuperset", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "T \\ Z", "output": "T \\ Z", "fidelity": 1.0, "ast_type": "SetDifference", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "T ∩ C", "output": "T ∩ C", "fidelity": 1.0, "ast_type": "SetIntersection", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "z ∈ B", "output": "z ∈ B", "fidelity": 1.0, "ast_type": "SetMembership", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "Y ⊆ A", "output": "Y ⊆ A", "fidelity": 1.0, "ast_type": "SetSubset", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "a ∉ Y", "output": "a ∉ Y", "fidelity": 1.0, "ast_type": "SetNonMembership", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "∅", "output": "∅", "fidelity": 1.0, "ast_type": "EmptySet", "category": "set_theory", "generated": true, "complexity": 0}, {"input": "|C|", "output": "|C|", "fidelity": 1.0, "ast_type": "SetCardinality", "category": "set_theory", "generated": true, "complexity": 0}, {"input": "∅", "output": "∅", "fidelity": 1.0, "ast_type": "EmptySet", "category": "set_theory", "generated": true, "complexity": 0}, {"input": "A ⊆ T", "output": "A ⊆ T", "fidelity": 1.0, "ast_type": "SetSubset", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "a ∈ Y", "output": "a ∈ Y", "fidelity": 1.0, "ast_type": "SetMembership", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "A ⊆ S", "output": "A ⊆ S", "fidelity": 1.0, "ast_type": "SetSubset", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "∅", "output": "∅", "fidelity": 1.0, "ast_type": "EmptySet", "category": "set_theory", "generated": true, "complexity": 0}, {"input": "A ⊇ X", "output": "A ⊇ X", "fidelity": 1.0, "ast_type": "SetSuperset", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "Z \\ A", "output": "Z \\ A", "fidelity": 1.0, "ast_type": "SetDifference", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "c ∈ B", "output": "c ∈ B", "fidelity": 1.0, "ast_type": "SetMembership", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "∅", "output": "∅", "fidelity": 1.0, "ast_type": "EmptySet", "category": "set_theory", "generated": true, "complexity": 0}, {"input": "b ∈ X", "output": "b ∈ X", "fidelity": 1.0, "ast_type": "SetMembership", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "A ⊇ B", "output": "A ⊇ B", "fidelity": 1.0, "ast_type": "SetSuperset", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "C \\ S", "output": "C \\ S", "fidelity": 1.0, "ast_type": "SetDifference", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "C ⊆ X", "output": "C ⊆ X", "fidelity": 1.0, "ast_type": "SetSubset", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "|B|", "output": "|B|", "fidelity": 1.0, "ast_type": "SetCardinality", "category": "set_theory", "generated": true, "complexity": 0}, {"input": "B ∪ B", "output": "B ∪ B", "fidelity": 1.0, "ast_type": "SetUnion", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "A ∩ Y", "output": "A ∩ Y", "fidelity": 1.0, "ast_type": "SetIntersection", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "Y ∪ T", "output": "Y ∪ T", "fidelity": 1.0, "ast_type": "SetUnion", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "Z ∪ B", "output": "Z ∪ B", "fidelity": 1.0, "ast_type": "SetUnion", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "𝒫(T)", "output": "𝒫(T)", "fidelity": 1.0, "ast_type": "PowerSet", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "C \\ A", "output": "C \\ A", "fidelity": 1.0, "ast_type": "SetDifference", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "B ⊇ X", "output": "B ⊇ X", "fidelity": 1.0, "ast_type": "SetSuperset", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "Z ⊆ S", "output": "Z ⊆ S", "fidelity": 1.0, "ast_type": "SetSubset", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "X ⊆ C", "output": "X ⊆ C", "fidelity": 1.0, "ast_type": "SetSubset", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "S ⊇ T", "output": "S ⊇ T", "fidelity": 1.0, "ast_type": "SetSuperset", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "x ∈ X", "output": "x ∈ X", "fidelity": 1.0, "ast_type": "SetMembership", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "B ∩ S", "output": "B ∩ S", "fidelity": 1.0, "ast_type": "SetIntersection", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "a ∈ C", "output": "a ∈ C", "fidelity": 1.0, "ast_type": "SetMembership", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "Z ∪ T", "output": "Z ∪ T", "fidelity": 1.0, "ast_type": "SetUnion", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "|Y|", "output": "|Y|", "fidelity": 1.0, "ast_type": "SetCardinality", "category": "set_theory", "generated": true, "complexity": 0}, {"input": "|B|", "output": "|B|", "fidelity": 1.0, "ast_type": "SetCardinality", "category": "set_theory", "generated": true, "complexity": 0}, {"input": "∅", "output": "∅", "fidelity": 1.0, "ast_type": "EmptySet", "category": "set_theory", "generated": true, "complexity": 0}, {"input": "x ∈ X", "output": "x ∈ X", "fidelity": 1.0, "ast_type": "SetMembership", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "S ∩ C", "output": "S ∩ C", "fidelity": 1.0, "ast_type": "SetIntersection", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "B ⊆ C", "output": "B ⊆ C", "fidelity": 1.0, "ast_type": "SetSubset", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "a ∈ C", "output": "a ∈ C", "fidelity": 1.0, "ast_type": "SetMembership", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "∅", "output": "∅", "fidelity": 1.0, "ast_type": "EmptySet", "category": "set_theory", "generated": true, "complexity": 0}, {"input": "z ∉ B", "output": "z ∉ B", "fidelity": 1.0, "ast_type": "SetNonMembership", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "T ∪ Y", "output": "T ∪ Y", "fidelity": 1.0, "ast_type": "SetUnion", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "X ⊆ Y", "output": "X ⊆ Y", "fidelity": 1.0, "ast_type": "SetSubset", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "Y ∩ Z", "output": "Y ∩ Z", "fidelity": 1.0, "ast_type": "SetIntersection", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "T ∪ X", "output": "T ∪ X", "fidelity": 1.0, "ast_type": "SetUnion", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "z ∉ B", "output": "z ∉ B", "fidelity": 1.0, "ast_type": "SetNonMembership", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "Y ⊇ A", "output": "Y ⊇ A", "fidelity": 1.0, "ast_type": "SetSuperset", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "A ⊇ X", "output": "A ⊇ X", "fidelity": 1.0, "ast_type": "SetSuperset", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "B ∪ T", "output": "B ∪ T", "fidelity": 1.0, "ast_type": "SetUnion", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "|T|", "output": "|T|", "fidelity": 1.0, "ast_type": "SetCardinality", "category": "set_theory", "generated": true, "complexity": 0}, {"input": "T ∩ X", "output": "T ∩ X", "fidelity": 1.0, "ast_type": "SetIntersection", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "T ∪ Z", "output": "T ∪ Z", "fidelity": 1.0, "ast_type": "SetUnion", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "|T|", "output": "|T|", "fidelity": 1.0, "ast_type": "SetCardinality", "category": "set_theory", "generated": true, "complexity": 0}, {"input": "𝒫(C)", "output": "𝒫(C)", "fidelity": 1.0, "ast_type": "PowerSet", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "b ∉ X", "output": "b ∉ X", "fidelity": 1.0, "ast_type": "SetNonMembership", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "c ∈ Z", "output": "c ∈ Z", "fidelity": 1.0, "ast_type": "SetMembership", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "z ∉ S", "output": "z ∉ S", "fidelity": 1.0, "ast_type": "SetNonMembership", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "𝒫(A)", "output": "𝒫(A)", "fidelity": 1.0, "ast_type": "PowerSet", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "𝒫(A)", "output": "𝒫(A)", "fidelity": 1.0, "ast_type": "PowerSet", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "y ∉ Z", "output": "y ∉ Z", "fidelity": 1.0, "ast_type": "SetNonMembership", "category": "set_theory", "generated": true, "complexity": 2}, {"input": "∂g(x)/∂x", "output": "∂g(x)/∂x", "fidelity": 1.0, "ast_type": "PartialDerivative", "category": "mathematical_analysis", "generated": true, "complexity": 2}, {"input": "∫_b^1 cos(x) dz", "output": "∫_b^1 cos(x) dz", "fidelity": 1.0, "ast_type": "DefiniteIntegral", "category": "mathematical_analysis", "generated": true, "complexity": 4}, {"input": "∫ f(x) dx", "output": "∫ f(x) dx", "fidelity": 1.0, "ast_type": "Integral", "category": "mathematical_analysis", "generated": true, "complexity": 4}, {"input": "∏_y=0^∞ a_n", "output": "∏_y=0^∞ a_n", "fidelity": 1.0, "ast_type": "Product", "category": "mathematical_analysis", "generated": true, "complexity": 1}, {"input": "∆h(x)", "output": "∆h(x)", "fidelity": 1.0, "ast_type": "Lapla<PERSON>", "category": "mathematical_analysis", "generated": true, "complexity": 2}, {"input": "∏_t=1^∞ x^n", "output": "∏_t=1^∞ x^n", "fidelity": 1.0, "ast_type": "Product", "category": "mathematical_analysis", "generated": true, "complexity": 1}, {"input": "∆sin(x)", "output": "∆sin(x)", "fidelity": 1.0, "ast_type": "Lapla<PERSON>", "category": "mathematical_analysis", "generated": true, "complexity": 2}, {"input": "∂ln(x)/∂x", "output": "∂ln(x)/∂x", "fidelity": 1.0, "ast_type": "PartialDerivative", "category": "mathematical_analysis", "generated": true, "complexity": 2}, {"input": "∫ ln(x) dx", "output": "∫ ln(x) dx", "fidelity": 1.0, "ast_type": "Integral", "category": "mathematical_analysis", "generated": true, "complexity": 4}, {"input": "∑_t=1^∞ f(n)", "output": "∑_t=1^∞ f(n)", "fidelity": 1.0, "ast_type": "Summation", "category": "mathematical_analysis", "generated": true, "complexity": 3}, {"input": "∮ f(x) dt", "output": "∮ f(x) dt", "fidelity": 1.0, "ast_type": "ContourIntegral", "category": "mathematical_analysis", "generated": true, "complexity": 4}, {"input": "∏_z=0^N a_n", "output": "∏_z=0^N a_n", "fidelity": 1.0, "ast_type": "Product", "category": "mathematical_analysis", "generated": true, "complexity": 1}, {"input": "∏_t=n^N f(n)", "output": "∏_t=n^N f(n)", "fidelity": 1.0, "ast_type": "Product", "category": "mathematical_analysis", "generated": true, "complexity": 3}, {"input": "lim_z→1 g(x)", "output": "lim_z→1 g(x)", "fidelity": 1.0, "ast_type": "Limit", "category": "mathematical_analysis", "generated": true, "complexity": 3}, {"input": "lim_z→a e^x", "output": "lim_z→a e^x", "fidelity": 1.0, "ast_type": "Limit", "category": "mathematical_analysis", "generated": true, "complexity": 1}, {"input": "∏_y=0^N x^n", "output": "∏_y=0^N x^n", "fidelity": 1.0, "ast_type": "Product", "category": "mathematical_analysis", "generated": true, "complexity": 1}, {"input": "∇e^x", "output": "∇e^x", "fidelity": 1.0, "ast_type": "Gradient", "category": "mathematical_analysis", "generated": true, "complexity": 0}, {"input": "∑_t=0^n a_n", "output": "∑_t=0^n a_n", "fidelity": 1.0, "ast_type": "Summation", "category": "mathematical_analysis", "generated": true, "complexity": 1}, {"input": "∑_y=n^n f(n)", "output": "∑_y=n^n f(n)", "fidelity": 1.0, "ast_type": "Summation", "category": "mathematical_analysis", "generated": true, "complexity": 3}, {"input": "∑_x=n^∞ a_n", "output": "∑_x=n^∞ a_n", "fidelity": 1.0, "ast_type": "Summation", "category": "mathematical_analysis", "generated": true, "complexity": 1}, {"input": "∇cos(x)", "output": "∇cos(x)", "fidelity": 1.0, "ast_type": "Gradient", "category": "mathematical_analysis", "generated": true, "complexity": 2}, {"input": "∇e^x", "output": "∇e^x", "fidelity": 1.0, "ast_type": "Gradient", "category": "mathematical_analysis", "generated": true, "complexity": 0}, {"input": "∫ ln(x) dy", "output": "∫ ln(x) dy", "fidelity": 1.0, "ast_type": "Integral", "category": "mathematical_analysis", "generated": true, "complexity": 4}, {"input": "∆sin(x)", "output": "∆sin(x)", "fidelity": 1.0, "ast_type": "Lapla<PERSON>", "category": "mathematical_analysis", "generated": true, "complexity": 2}, {"input": "∑_x=n^N f(n)", "output": "∑_x=n^N f(n)", "fidelity": 1.0, "ast_type": "Summation", "category": "mathematical_analysis", "generated": true, "complexity": 3}, {"input": "∫_b^a cos(x) dt", "output": "∫_b^a cos(x) dt", "fidelity": 1.0, "ast_type": "DefiniteIntegral", "category": "mathematical_analysis", "generated": true, "complexity": 4}, {"input": "∏_x=1^N a_n", "output": "∏_x=1^N a_n", "fidelity": 1.0, "ast_type": "Product", "category": "mathematical_analysis", "generated": true, "complexity": 1}, {"input": "∂e^x/∂t", "output": "∂e^x/∂t", "fidelity": 1.0, "ast_type": "PartialDerivative", "category": "mathematical_analysis", "generated": true, "complexity": 0}, {"input": "∫ sin(x) dy", "output": "∫ sin(x) dy", "fidelity": 1.0, "ast_type": "Integral", "category": "mathematical_analysis", "generated": true, "complexity": 4}, {"input": "∫ ln(x) dz", "output": "∫ ln(x) dz", "fidelity": 1.0, "ast_type": "Integral", "category": "mathematical_analysis", "generated": true, "complexity": 4}, {"input": "lim_x→1 g(x)", "output": "lim_x→1 g(x)", "fidelity": 1.0, "ast_type": "Limit", "category": "mathematical_analysis", "generated": true, "complexity": 3}, {"input": "∇cos(x)", "output": "∇cos(x)", "fidelity": 1.0, "ast_type": "Gradient", "category": "mathematical_analysis", "generated": true, "complexity": 2}, {"input": "dcos(x)/dy", "output": "dcos(x)/dy", "fidelity": 1.0, "ast_type": "Derivative", "category": "mathematical_analysis", "generated": true, "complexity": 2}, {"input": "∫_1^1 ln(x) dx", "output": "∫_1^1 ln(x) dx", "fidelity": 1.0, "ast_type": "DefiniteIntegral", "category": "mathematical_analysis", "generated": true, "complexity": 4}, {"input": "∮ h(x) dt", "output": "∮ h(x) dt", "fidelity": 1.0, "ast_type": "ContourIntegral", "category": "mathematical_analysis", "generated": true, "complexity": 4}, {"input": "∮ cos(x) dt", "output": "∮ cos(x) dt", "fidelity": 1.0, "ast_type": "ContourIntegral", "category": "mathematical_analysis", "generated": true, "complexity": 4}, {"input": "lim_t→1 sin(x)", "output": "lim_t→1 sin(x)", "fidelity": 1.0, "ast_type": "Limit", "category": "mathematical_analysis", "generated": true, "complexity": 3}, {"input": "∏_t=1^n a_n", "output": "∏_t=1^n a_n", "fidelity": 1.0, "ast_type": "Product", "category": "mathematical_analysis", "generated": true, "complexity": 1}, {"input": "∫_1^1 sin(x) dy", "output": "∫_1^1 sin(x) dy", "fidelity": 1.0, "ast_type": "DefiniteIntegral", "category": "mathematical_analysis", "generated": true, "complexity": 4}, {"input": "dln(x)/dt", "output": "dln(x)/dt", "fidelity": 1.0, "ast_type": "Derivative", "category": "mathematical_analysis", "generated": true, "complexity": 2}, {"input": "∏_x=n^n x^n", "output": "∏_x=n^n x^n", "fidelity": 1.0, "ast_type": "Product", "category": "mathematical_analysis", "generated": true, "complexity": 1}, {"input": "∫_b^a cos(x) dx", "output": "∫_b^a cos(x) dx", "fidelity": 1.0, "ast_type": "DefiniteIntegral", "category": "mathematical_analysis", "generated": true, "complexity": 4}, {"input": "∂ln(x)/∂x", "output": "∂ln(x)/∂x", "fidelity": 1.0, "ast_type": "PartialDerivative", "category": "mathematical_analysis", "generated": true, "complexity": 2}, {"input": "∫_1^a g(x) dt", "output": "∫_1^a g(x) dt", "fidelity": 1.0, "ast_type": "DefiniteIntegral", "category": "mathematical_analysis", "generated": true, "complexity": 4}, {"input": "∫_b^a f(x) dz", "output": "∫_b^a f(x) dz", "fidelity": 1.0, "ast_type": "DefiniteIntegral", "category": "mathematical_analysis", "generated": true, "complexity": 4}, {"input": "∑_y=0^∞ x^n", "output": "∑_y=0^∞ x^n", "fidelity": 1.0, "ast_type": "Summation", "category": "mathematical_analysis", "generated": true, "complexity": 1}, {"input": "∂sin(x)/∂t", "output": "∂sin(x)/∂t", "fidelity": 1.0, "ast_type": "PartialDerivative", "category": "mathematical_analysis", "generated": true, "complexity": 2}, {"input": "∇g(x)", "output": "∇g(x)", "fidelity": 1.0, "ast_type": "Gradient", "category": "mathematical_analysis", "generated": true, "complexity": 2}, {"input": "de^x/dx", "output": "de^x/dx", "fidelity": 1.0, "ast_type": "Derivative", "category": "mathematical_analysis", "generated": true, "complexity": 0}, {"input": "∇sin(x)", "output": "∇sin(x)", "fidelity": 1.0, "ast_type": "Gradient", "category": "mathematical_analysis", "generated": true, "complexity": 2}, {"input": "∇ln(x)", "output": "∇ln(x)", "fidelity": 1.0, "ast_type": "Gradient", "category": "mathematical_analysis", "generated": true, "complexity": 2}, {"input": "df(x)/dz", "output": "df(x)/dz", "fidelity": 1.0, "ast_type": "Derivative", "category": "mathematical_analysis", "generated": true, "complexity": 2}, {"input": "∑_z=1^n f(n)", "output": "∑_z=1^n f(n)", "fidelity": 1.0, "ast_type": "Summation", "category": "mathematical_analysis", "generated": true, "complexity": 3}, {"input": "∆h(x)", "output": "∆h(x)", "fidelity": 1.0, "ast_type": "Lapla<PERSON>", "category": "mathematical_analysis", "generated": true, "complexity": 2}, {"input": "df(x)/dz", "output": "df(x)/dz", "fidelity": 1.0, "ast_type": "Derivative", "category": "mathematical_analysis", "generated": true, "complexity": 2}, {"input": "∇sin(x)", "output": "∇sin(x)", "fidelity": 1.0, "ast_type": "Gradient", "category": "mathematical_analysis", "generated": true, "complexity": 2}, {"input": "∇ln(x)", "output": "∇ln(x)", "fidelity": 1.0, "ast_type": "Gradient", "category": "mathematical_analysis", "generated": true, "complexity": 2}, {"input": "∫_a^a sin(x) dz", "output": "∫_a^a sin(x) dz", "fidelity": 1.0, "ast_type": "DefiniteIntegral", "category": "mathematical_analysis", "generated": true, "complexity": 4}, {"input": "∑_z=0^∞ a_n", "output": "∑_z=0^∞ a_n", "fidelity": 1.0, "ast_type": "Summation", "category": "mathematical_analysis", "generated": true, "complexity": 1}, {"input": "dln(x)/dy", "output": "dln(x)/dy", "fidelity": 1.0, "ast_type": "Derivative", "category": "mathematical_analysis", "generated": true, "complexity": 2}, {"input": "∫ ln(x) dx", "output": "∫ ln(x) dx", "fidelity": 1.0, "ast_type": "Integral", "category": "mathematical_analysis", "generated": true, "complexity": 4}, {"input": "∏_t=n^∞ a_n", "output": "∏_t=n^∞ a_n", "fidelity": 1.0, "ast_type": "Product", "category": "mathematical_analysis", "generated": true, "complexity": 1}, {"input": "∇sin(x)", "output": "∇sin(x)", "fidelity": 1.0, "ast_type": "Gradient", "category": "mathematical_analysis", "generated": true, "complexity": 2}, {"input": "∫ sin(x) dy", "output": "∫ sin(x) dy", "fidelity": 1.0, "ast_type": "Integral", "category": "mathematical_analysis", "generated": true, "complexity": 4}, {"input": "∆ln(x)", "output": "∆ln(x)", "fidelity": 1.0, "ast_type": "Lapla<PERSON>", "category": "mathematical_analysis", "generated": true, "complexity": 2}, {"input": "lim_z→1 sin(x)", "output": "lim_z→1 sin(x)", "fidelity": 1.0, "ast_type": "Limit", "category": "mathematical_analysis", "generated": true, "complexity": 3}, {"input": "∮ f(x) dy", "output": "∮ f(x) dy", "fidelity": 1.0, "ast_type": "ContourIntegral", "category": "mathematical_analysis", "generated": true, "complexity": 4}, {"input": "∂ln(x)/∂z", "output": "∂ln(x)/∂z", "fidelity": 1.0, "ast_type": "PartialDerivative", "category": "mathematical_analysis", "generated": true, "complexity": 2}, {"input": "dg(x)/dx", "output": "dg(x)/dx", "fidelity": 1.0, "ast_type": "Derivative", "category": "mathematical_analysis", "generated": true, "complexity": 2}, {"input": "∮ cos(x) dy", "output": "∮ cos(x) dy", "fidelity": 1.0, "ast_type": "ContourIntegral", "category": "mathematical_analysis", "generated": true, "complexity": 4}, {"input": "∆g(x)", "output": "∆g(x)", "fidelity": 1.0, "ast_type": "Lapla<PERSON>", "category": "mathematical_analysis", "generated": true, "complexity": 2}, {"input": "∂g(x)/∂z", "output": "∂g(x)/∂z", "fidelity": 1.0, "ast_type": "PartialDerivative", "category": "mathematical_analysis", "generated": true, "complexity": 2}, {"input": "∏_t=1^n f(n)", "output": "∏_t=1^n f(n)", "fidelity": 1.0, "ast_type": "Product", "category": "mathematical_analysis", "generated": true, "complexity": 3}, {"input": "∮ cos(x) dz", "output": "∮ cos(x) dz", "fidelity": 1.0, "ast_type": "ContourIntegral", "category": "mathematical_analysis", "generated": true, "complexity": 4}, {"input": "lim_x→0 h(x)", "output": "lim_x→0 h(x)", "fidelity": 1.0, "ast_type": "Limit", "category": "mathematical_analysis", "generated": true, "complexity": 3}, {"input": "∮ ln(x) dy", "output": "∮ ln(x) dy", "fidelity": 1.0, "ast_type": "ContourIntegral", "category": "mathematical_analysis", "generated": true, "complexity": 4}, {"input": "dh(x)/dy", "output": "dh(x)/dy", "fidelity": 1.0, "ast_type": "Derivative", "category": "mathematical_analysis", "generated": true, "complexity": 2}, {"input": "∏_t=n^∞ f(n)", "output": "∏_t=n^∞ f(n)", "fidelity": 1.0, "ast_type": "Product", "category": "mathematical_analysis", "generated": true, "complexity": 3}, {"input": "lim_y→0 ln(x)", "output": "lim_y→0 ln(x)", "fidelity": 1.0, "ast_type": "Limit", "category": "mathematical_analysis", "generated": true, "complexity": 3}, {"input": "∑_t=n^n f(n)", "output": "∑_t=n^n f(n)", "fidelity": 1.0, "ast_type": "Summation", "category": "mathematical_analysis", "generated": true, "complexity": 3}, {"input": "∑_t=1^N f(n)", "output": "∑_t=1^N f(n)", "fidelity": 1.0, "ast_type": "Summation", "category": "mathematical_analysis", "generated": true, "complexity": 3}, {"input": "df(x)/dz", "output": "df(x)/dz", "fidelity": 1.0, "ast_type": "Derivative", "category": "mathematical_analysis", "generated": true, "complexity": 2}, {"input": "∂cos(x)/∂t", "output": "∂cos(x)/∂t", "fidelity": 1.0, "ast_type": "PartialDerivative", "category": "mathematical_analysis", "generated": true, "complexity": 2}, {"input": "∆sin(x)", "output": "∆sin(x)", "fidelity": 1.0, "ast_type": "Lapla<PERSON>", "category": "mathematical_analysis", "generated": true, "complexity": 2}, {"input": "∫_∞^1 f(x) dz", "output": "∫_∞^1 f(x) dz", "fidelity": 1.0, "ast_type": "DefiniteIntegral", "category": "mathematical_analysis", "generated": true, "complexity": 4}, {"input": "dh(x)/dz", "output": "dh(x)/dz", "fidelity": 1.0, "ast_type": "Derivative", "category": "mathematical_analysis", "generated": true, "complexity": 2}, {"input": "∏_z=1^N a_n", "output": "∏_z=1^N a_n", "fidelity": 1.0, "ast_type": "Product", "category": "mathematical_analysis", "generated": true, "complexity": 1}, {"input": "∂e^x/∂z", "output": "∂e^x/∂z", "fidelity": 1.0, "ast_type": "PartialDerivative", "category": "mathematical_analysis", "generated": true, "complexity": 0}, {"input": "∮ ln(x) dx", "output": "∮ ln(x) dx", "fidelity": 1.0, "ast_type": "ContourIntegral", "category": "mathematical_analysis", "generated": true, "complexity": 4}, {"input": "∑_x=0^N x^n", "output": "∑_x=0^N x^n", "fidelity": 1.0, "ast_type": "Summation", "category": "mathematical_analysis", "generated": true, "complexity": 1}, {"input": "∑_t=n^N a_n", "output": "∑_t=n^N a_n", "fidelity": 1.0, "ast_type": "Summation", "category": "mathematical_analysis", "generated": true, "complexity": 1}, {"input": "∏_z=1^∞ f(n)", "output": "∏_z=1^∞ f(n)", "fidelity": 1.0, "ast_type": "Product", "category": "mathematical_analysis", "generated": true, "complexity": 3}, {"input": "∑_z=1^n f(n)", "output": "∑_z=1^n f(n)", "fidelity": 1.0, "ast_type": "Summation", "category": "mathematical_analysis", "generated": true, "complexity": 3}, {"input": "∫_1^∞ e^x dz", "output": "∫_1^∞ e^x dz", "fidelity": 1.0, "ast_type": "DefiniteIntegral", "category": "mathematical_analysis", "generated": true, "complexity": 2}, {"input": "lim_y→a sin(x)", "output": "lim_y→a sin(x)", "fidelity": 1.0, "ast_type": "Limit", "category": "mathematical_analysis", "generated": true, "complexity": 3}, {"input": "∇sin(x)", "output": "∇sin(x)", "fidelity": 1.0, "ast_type": "Gradient", "category": "mathematical_analysis", "generated": true, "complexity": 2}, {"input": "lim_y→a f(x)", "output": "lim_y→a f(x)", "fidelity": 1.0, "ast_type": "Limit", "category": "mathematical_analysis", "generated": true, "complexity": 3}, {"input": "∆cos(x)", "output": "∆cos(x)", "fidelity": 1.0, "ast_type": "Lapla<PERSON>", "category": "mathematical_analysis", "generated": true, "complexity": 2}, {"input": "lim_t→b sin(x)", "output": "lim_t→b sin(x)", "fidelity": 1.0, "ast_type": "Limit", "category": "mathematical_analysis", "generated": true, "complexity": 3}, {"input": "∇e^x", "output": "∇e^x", "fidelity": 1.0, "ast_type": "Gradient", "category": "mathematical_analysis", "generated": true, "complexity": 0}, {"input": "∆ln(x)", "output": "∆ln(x)", "fidelity": 1.0, "ast_type": "Lapla<PERSON>", "category": "mathematical_analysis", "generated": true, "complexity": 2}, {"input": "∑_x=0^∞ a_n", "output": "∑_x=0^∞ a_n", "fidelity": 1.0, "ast_type": "Summation", "category": "mathematical_analysis", "generated": true, "complexity": 1}, {"input": "∮ h(x) dx", "output": "∮ h(x) dx", "fidelity": 1.0, "ast_type": "ContourIntegral", "category": "mathematical_analysis", "generated": true, "complexity": 4}, {"input": "∏_x=0^N x^n", "output": "∏_x=0^N x^n", "fidelity": 1.0, "ast_type": "Product", "category": "mathematical_analysis", "generated": true, "complexity": 1}, {"input": "∆h(x)", "output": "∆h(x)", "fidelity": 1.0, "ast_type": "Lapla<PERSON>", "category": "mathematical_analysis", "generated": true, "complexity": 2}, {"input": "∆h(x)", "output": "∆h(x)", "fidelity": 1.0, "ast_type": "Lapla<PERSON>", "category": "mathematical_analysis", "generated": true, "complexity": 2}, {"input": "∆sin(x)", "output": "∆sin(x)", "fidelity": 1.0, "ast_type": "Lapla<PERSON>", "category": "mathematical_analysis", "generated": true, "complexity": 2}, {"input": "∫ g(x) dx", "output": "∫ g(x) dx", "fidelity": 1.0, "ast_type": "Integral", "category": "mathematical_analysis", "generated": true, "complexity": 4}, {"input": "∫ cos(x) dt", "output": "∫ cos(x) dt", "fidelity": 1.0, "ast_type": "Integral", "category": "mathematical_analysis", "generated": true, "complexity": 4}, {"input": "∫ ln(x) dz", "output": "∫ ln(x) dz", "fidelity": 1.0, "ast_type": "Integral", "category": "mathematical_analysis", "generated": true, "complexity": 4}, {"input": "∑_z=0^N f(n)", "output": "∑_z=0^N f(n)", "fidelity": 1.0, "ast_type": "Summation", "category": "mathematical_analysis", "generated": true, "complexity": 3}, {"input": "lim_y→∞ ln(x)", "output": "lim_y→∞ ln(x)", "fidelity": 1.0, "ast_type": "Limit", "category": "mathematical_analysis", "generated": true, "complexity": 3}, {"input": "∏_y=0^n f(n)", "output": "∏_y=0^n f(n)", "fidelity": 1.0, "ast_type": "Product", "category": "mathematical_analysis", "generated": true, "complexity": 3}, {"input": "∮ e^x dz", "output": "∮ e^x dz", "fidelity": 1.0, "ast_type": "ContourIntegral", "category": "mathematical_analysis", "generated": true, "complexity": 2}, {"input": "df(x)/dx", "output": "df(x)/dx", "fidelity": 1.0, "ast_type": "Derivative", "category": "mathematical_analysis", "generated": true, "complexity": 2}, {"input": "∫_1^0 ln(x) dz", "output": "∫_1^0 ln(x) dz", "fidelity": 1.0, "ast_type": "DefiniteIntegral", "category": "mathematical_analysis", "generated": true, "complexity": 4}, {"input": "∏_y=0^N a_n", "output": "∏_y=0^N a_n", "fidelity": 1.0, "ast_type": "Product", "category": "mathematical_analysis", "generated": true, "complexity": 1}, {"input": "∮ g(x) dz", "output": "∮ g(x) dz", "fidelity": 1.0, "ast_type": "ContourIntegral", "category": "mathematical_analysis", "generated": true, "complexity": 4}, {"input": "dsin(x)/dy", "output": "dsin(x)/dy", "fidelity": 1.0, "ast_type": "Derivative", "category": "mathematical_analysis", "generated": true, "complexity": 2}, {"input": "∂f(x)/∂t", "output": "∂f(x)/∂t", "fidelity": 1.0, "ast_type": "PartialDerivative", "category": "mathematical_analysis", "generated": true, "complexity": 2}, {"input": "dg(x)/dy", "output": "dg(x)/dy", "fidelity": 1.0, "ast_type": "Derivative", "category": "mathematical_analysis", "generated": true, "complexity": 2}, {"input": "dln(x)/dy", "output": "dln(x)/dy", "fidelity": 1.0, "ast_type": "Derivative", "category": "mathematical_analysis", "generated": true, "complexity": 2}, {"input": "∏_x=0^n f(n)", "output": "∏_x=0^n f(n)", "fidelity": 1.0, "ast_type": "Product", "category": "mathematical_analysis", "generated": true, "complexity": 3}, {"input": "dln(x)/dz", "output": "dln(x)/dz", "fidelity": 1.0, "ast_type": "Derivative", "category": "mathematical_analysis", "generated": true, "complexity": 2}, {"input": "df(x)/dx", "output": "df(x)/dx", "fidelity": 1.0, "ast_type": "Derivative", "category": "mathematical_analysis", "generated": true, "complexity": 2}, {"input": "∑_t=n^∞ x^n", "output": "∑_t=n^∞ x^n", "fidelity": 1.0, "ast_type": "Summation", "category": "mathematical_analysis", "generated": true, "complexity": 1}, {"input": "∮ e^x dz", "output": "∮ e^x dz", "fidelity": 1.0, "ast_type": "ContourIntegral", "category": "mathematical_analysis", "generated": true, "complexity": 2}, {"input": "dcos(x)/dz", "output": "dcos(x)/dz", "fidelity": 1.0, "ast_type": "Derivative", "category": "mathematical_analysis", "generated": true, "complexity": 2}, {"input": "∫_∞^1 h(x) dt", "output": "∫_∞^1 h(x) dt", "fidelity": 1.0, "ast_type": "DefiniteIntegral", "category": "mathematical_analysis", "generated": true, "complexity": 4}, {"input": "∫_1^a g(x) dt", "output": "∫_1^a g(x) dt", "fidelity": 1.0, "ast_type": "DefiniteIntegral", "category": "mathematical_analysis", "generated": true, "complexity": 4}, {"input": "∂cos(x)/∂t", "output": "∂cos(x)/∂t", "fidelity": 1.0, "ast_type": "PartialDerivative", "category": "mathematical_analysis", "generated": true, "complexity": 2}, {"input": "dcos(x)/dz", "output": "dcos(x)/dz", "fidelity": 1.0, "ast_type": "Derivative", "category": "mathematical_analysis", "generated": true, "complexity": 2}, {"input": "∆f(x)", "output": "∆f(x)", "fidelity": 1.0, "ast_type": "Lapla<PERSON>", "category": "mathematical_analysis", "generated": true, "complexity": 2}, {"input": "∏_x=1^N x^n", "output": "∏_x=1^N x^n", "fidelity": 1.0, "ast_type": "Product", "category": "mathematical_analysis", "generated": true, "complexity": 1}, {"input": "∆sin(x)", "output": "∆sin(x)", "fidelity": 1.0, "ast_type": "Lapla<PERSON>", "category": "mathematical_analysis", "generated": true, "complexity": 2}, {"input": "df(x)/dx", "output": "df(x)/dx", "fidelity": 1.0, "ast_type": "Derivative", "category": "mathematical_analysis", "generated": true, "complexity": 2}, {"input": "∫_0^a h(x) dz", "output": "∫_0^a h(x) dz", "fidelity": 1.0, "ast_type": "DefiniteIntegral", "category": "mathematical_analysis", "generated": true, "complexity": 4}, {"input": "lim_y→a f(x)", "output": "lim_y→a f(x)", "fidelity": 1.0, "ast_type": "Limit", "category": "mathematical_analysis", "generated": true, "complexity": 3}, {"input": "∇sin(x)", "output": "∇sin(x)", "fidelity": 1.0, "ast_type": "Gradient", "category": "mathematical_analysis", "generated": true, "complexity": 2}, {"input": "∇e^x", "output": "∇e^x", "fidelity": 1.0, "ast_type": "Gradient", "category": "mathematical_analysis", "generated": true, "complexity": 0}, {"input": "∑_z=1^∞ x^n", "output": "∑_z=1^∞ x^n", "fidelity": 1.0, "ast_type": "Summation", "category": "mathematical_analysis", "generated": true, "complexity": 1}, {"input": "∇g(x)", "output": "∇g(x)", "fidelity": 1.0, "ast_type": "Gradient", "category": "mathematical_analysis", "generated": true, "complexity": 2}, {"input": "lim_y→1 ln(x)", "output": "lim_y→1 ln(x)", "fidelity": 1.0, "ast_type": "Limit", "category": "mathematical_analysis", "generated": true, "complexity": 3}, {"input": "∆h(x)", "output": "∆h(x)", "fidelity": 1.0, "ast_type": "Lapla<PERSON>", "category": "mathematical_analysis", "generated": true, "complexity": 2}, {"input": "dg(x)/dx", "output": "dg(x)/dx", "fidelity": 1.0, "ast_type": "Derivative", "category": "mathematical_analysis", "generated": true, "complexity": 2}, {"input": "dsin(x)/dx", "output": "dsin(x)/dx", "fidelity": 1.0, "ast_type": "Derivative", "category": "mathematical_analysis", "generated": true, "complexity": 2}, {"input": "∮ cos(x) dz", "output": "∮ cos(x) dz", "fidelity": 1.0, "ast_type": "ContourIntegral", "category": "mathematical_analysis", "generated": true, "complexity": 4}, {"input": "dcos(x)/dx", "output": "dcos(x)/dx", "fidelity": 1.0, "ast_type": "Derivative", "category": "mathematical_analysis", "generated": true, "complexity": 2}, {"input": "∮ sin(x) dt", "output": "∮ sin(x) dt", "fidelity": 1.0, "ast_type": "ContourIntegral", "category": "mathematical_analysis", "generated": true, "complexity": 4}, {"input": "∑_z=n^N a_n", "output": "∑_z=n^N a_n", "fidelity": 1.0, "ast_type": "Summation", "category": "mathematical_analysis", "generated": true, "complexity": 1}, {"input": "(∀b: F(b)) ⇒ (∃b: R(b))", "output": "(∀b: F(b)) ⇒ (∃b: R(b))", "fidelity": 1.0, "ast_type": "ComplexImplication", "category": "logical_reasoning", "generated": true, "complexity": 15}, {"input": "¬(R ∨ R)", "output": "¬(R ∨ R)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true, "complexity": 5}, {"input": "∀k ∃k: R(k,k)", "output": "∀k ∃k: R(k,k)", "fidelity": 1.0, "ast_type": "MixedQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "R ∨ P ∨ H", "output": "R ∨ P ∨ H", "fidelity": 1.0, "ast_type": "LogicalDisjunction", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "¬(G ∧ R)", "output": "¬(G ∧ R)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true, "complexity": 5}, {"input": "∀x ∃a: R(x,a)", "output": "∀x ∃a: R(x,a)", "fidelity": 1.0, "ast_type": "MixedQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "F ⇔ Q", "output": "F ⇔ Q", "fidelity": 1.0, "ast_type": "LogicalEquivalence", "category": "logical_reasoning", "generated": true, "complexity": 3}, {"input": "(∀k: H(k)) ⇒ (∃k: R(k))", "output": "(∀k: H(k)) ⇒ (∃k: R(k))", "fidelity": 1.0, "ast_type": "ComplexImplication", "category": "logical_reasoning", "generated": true, "complexity": 15}, {"input": "∀k ∈ ℝ: S(k)", "output": "∀k ∈ ℝ: S(k)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "S ∧ H ∧ T", "output": "S ∧ H ∧ T", "fidelity": 1.0, "ast_type": "LogicalConjunction", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "G ∧ S ∧ G", "output": "G ∧ S ∧ G", "fidelity": 1.0, "ast_type": "LogicalConjunction", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∀a ∃b: T(a,b)", "output": "∀a ∃b: T(a,b)", "fidelity": 1.0, "ast_type": "MixedQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "R ∧ S ∧ G", "output": "R ∧ S ∧ G", "fidelity": 1.0, "ast_type": "LogicalConjunction", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∀c ∈ ℤ: F(c)", "output": "∀c ∈ ℤ: F(c)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "R ∨ P ∨ R", "output": "R ∨ P ∨ R", "fidelity": 1.0, "ast_type": "LogicalDisjunction", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "G ∧ S ∧ P", "output": "G ∧ S ∧ P", "fidelity": 1.0, "ast_type": "LogicalConjunction", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∃k ∈ ℤ: F(k)", "output": "∃k ∈ ℤ: F(k)", "fidelity": 1.0, "ast_type": "ExistentialQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "Q ∨ Q ∨ R", "output": "Q ∨ Q ∨ R", "fidelity": 1.0, "ast_type": "LogicalDisjunction", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "¬(P ∨ P)", "output": "¬(P ∨ P)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true, "complexity": 5}, {"input": "(Q ∧ H) ⇒ F", "output": "(Q ∧ H) ⇒ F", "fidelity": 1.0, "ast_type": "MaterialImplication", "category": "logical_reasoning", "generated": true, "complexity": 8}, {"input": "∀c ∈ ℝ: G(c)", "output": "∀c ∈ ℝ: G(c)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "H ⇔ Q", "output": "H ⇔ Q", "fidelity": 1.0, "ast_type": "LogicalEquivalence", "category": "logical_reasoning", "generated": true, "complexity": 3}, {"input": "(S ∧ Q) ⇒ F", "output": "(S ∧ Q) ⇒ F", "fidelity": 1.0, "ast_type": "MaterialImplication", "category": "logical_reasoning", "generated": true, "complexity": 8}, {"input": "T ∨ Q ∨ H", "output": "T ∨ Q ∨ H", "fidelity": 1.0, "ast_type": "LogicalDisjunction", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∀z ∃a: S(z,a)", "output": "∀z ∃a: S(z,a)", "fidelity": 1.0, "ast_type": "MixedQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "(∀b: Q(b)) ⇒ (∃b: T(b))", "output": "(∀b: Q(b)) ⇒ (∃b: T(b))", "fidelity": 1.0, "ast_type": "ComplexImplication", "category": "logical_reasoning", "generated": true, "complexity": 15}, {"input": "∃m ∈ ℕ: S(m)", "output": "∃m ∈ ℕ: S(m)", "fidelity": 1.0, "ast_type": "ExistentialQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∀y ∃x: R(y,x)", "output": "∀y ∃x: R(y,x)", "fidelity": 1.0, "ast_type": "MixedQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∀n ∀y: (Less(n,y) ⇒ Less(n,y))", "output": "∀n ∀y: (Less(n,y) ⇒ Less(n,y))", "fidelity": 1.0, "ast_type": "UniversalImplication", "category": "logical_reasoning", "generated": true, "complexity": 13}, {"input": "∀x ∃b: R(x,b)", "output": "∀x ∃b: R(x,b)", "fidelity": 1.0, "ast_type": "MixedQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "¬(R ∨ P)", "output": "¬(R ∨ P)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true, "complexity": 5}, {"input": "S ∧ T ∧ P", "output": "S ∧ T ∧ P", "fidelity": 1.0, "ast_type": "LogicalConjunction", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "¬(G ∧ F)", "output": "¬(G ∧ F)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true, "complexity": 5}, {"input": "∃y ∈ ℝ: F(y)", "output": "∃y ∈ ℝ: F(y)", "fidelity": 1.0, "ast_type": "ExistentialQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "H ∧ G ∧ Q", "output": "H ∧ G ∧ Q", "fidelity": 1.0, "ast_type": "LogicalConjunction", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "¬(P ∨ Q)", "output": "¬(P ∨ Q)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true, "complexity": 5}, {"input": "F ∨ Q ∨ F", "output": "F ∨ Q ∨ F", "fidelity": 1.0, "ast_type": "LogicalDisjunction", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "R ∨ H ∨ G", "output": "R ∨ H ∨ G", "fidelity": 1.0, "ast_type": "LogicalDisjunction", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "H ∧ F ∧ S", "output": "H ∧ F ∧ S", "fidelity": 1.0, "ast_type": "LogicalConjunction", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∃k ∈ ℤ: Q(k)", "output": "∃k ∈ ℤ: Q(k)", "fidelity": 1.0, "ast_type": "ExistentialQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∃b ∈ ℕ: R(b)", "output": "∃b ∈ ℕ: R(b)", "fidelity": 1.0, "ast_type": "ExistentialQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∀b ∃z: Less(b,z)", "output": "∀b ∃z: Less(b,z)", "fidelity": 1.0, "ast_type": "MixedQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∀c ∈ ℝ: T(c)", "output": "∀c ∈ ℝ: T(c)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "P ∨ H ∨ H", "output": "P ∨ H ∨ H", "fidelity": 1.0, "ast_type": "LogicalDisjunction", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "F ⇔ H", "output": "F ⇔ H", "fidelity": 1.0, "ast_type": "LogicalEquivalence", "category": "logical_reasoning", "generated": true, "complexity": 3}, {"input": "∀n ∀x: (Greater(n,x) ⇒ S(n,x))", "output": "∀n ∀x: (Greater(n,x) ⇒ S(n,x))", "fidelity": 1.0, "ast_type": "UniversalImplication", "category": "logical_reasoning", "generated": true, "complexity": 13}, {"input": "∀m ∀b: (Equal(m,b) ⇒ T(m,b))", "output": "∀m ∀b: (Equal(m,b) ⇒ T(m,b))", "fidelity": 1.0, "ast_type": "UniversalImplication", "category": "logical_reasoning", "generated": true, "complexity": 13}, {"input": "T ∨ G ∨ P", "output": "T ∨ G ∨ P", "fidelity": 1.0, "ast_type": "LogicalDisjunction", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "H ∧ R ∧ Q", "output": "H ∧ R ∧ Q", "fidelity": 1.0, "ast_type": "LogicalConjunction", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∀k ∈ ℕ: P(k)", "output": "∀k ∈ ℕ: P(k)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "T ∨ G ∨ P", "output": "T ∨ G ∨ P", "fidelity": 1.0, "ast_type": "LogicalDisjunction", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "T ⇒ T", "output": "T ⇒ T", "fidelity": 1.0, "ast_type": "MaterialImplication", "category": "logical_reasoning", "generated": true, "complexity": 3}, {"input": "R ⇒ G", "output": "R ⇒ G", "fidelity": 1.0, "ast_type": "MaterialImplication", "category": "logical_reasoning", "generated": true, "complexity": 3}, {"input": "Q ⇔ H", "output": "Q ⇔ H", "fidelity": 1.0, "ast_type": "LogicalEquivalence", "category": "logical_reasoning", "generated": true, "complexity": 3}, {"input": "P ∧ R ∧ F", "output": "P ∧ R ∧ F", "fidelity": 1.0, "ast_type": "LogicalConjunction", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "Q ⇔ Q", "output": "Q ⇔ Q", "fidelity": 1.0, "ast_type": "LogicalEquivalence", "category": "logical_reasoning", "generated": true, "complexity": 3}, {"input": "∀x ∃b: Equal(x,b)", "output": "∀x ∃b: Equal(x,b)", "fidelity": 1.0, "ast_type": "MixedQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "(∀k: P(k)) ⇒ (∃k: R(k))", "output": "(∀k: P(k)) ⇒ (∃k: R(k))", "fidelity": 1.0, "ast_type": "ComplexImplication", "category": "logical_reasoning", "generated": true, "complexity": 15}, {"input": "∀b ∀y: (Greater(b,y) ⇒ Equal(b,y))", "output": "∀b ∀y: (Greater(b,y) ⇒ Equal(b,y))", "fidelity": 1.0, "ast_type": "UniversalImplication", "category": "logical_reasoning", "generated": true, "complexity": 13}, {"input": "H ⇔ F", "output": "H ⇔ F", "fidelity": 1.0, "ast_type": "LogicalEquivalence", "category": "logical_reasoning", "generated": true, "complexity": 3}, {"input": "¬(H ∨ H)", "output": "¬(H ∨ H)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true, "complexity": 5}, {"input": "T ∧ Q ∧ P", "output": "T ∧ Q ∧ P", "fidelity": 1.0, "ast_type": "LogicalConjunction", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∃b ∈ ℕ: S(b)", "output": "∃b ∈ ℕ: S(b)", "fidelity": 1.0, "ast_type": "ExistentialQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "F ∨ H ∨ P", "output": "F ∨ H ∨ P", "fidelity": 1.0, "ast_type": "LogicalDisjunction", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∀n ∃n: Less(n,n)", "output": "∀n ∃n: Less(n,n)", "fidelity": 1.0, "ast_type": "MixedQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∀m ∈ ℚ: F(m)", "output": "∀m ∈ ℚ: F(m)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "¬(R ∨ T)", "output": "¬(R ∨ T)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true, "complexity": 5}, {"input": "(∀y: T(y)) ⇒ (∃y: R(y))", "output": "(∀y: T(y)) ⇒ (∃y: R(y))", "fidelity": 1.0, "ast_type": "ComplexImplication", "category": "logical_reasoning", "generated": true, "complexity": 15}, {"input": "¬(G ∧ G)", "output": "¬(G ∧ G)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true, "complexity": 5}, {"input": "(P ∧ P) ⇒ R", "output": "(P ∧ P) ⇒ R", "fidelity": 1.0, "ast_type": "MaterialImplication", "category": "logical_reasoning", "generated": true, "complexity": 8}, {"input": "(∀n: T(n)) ⇒ (∃n: G(n))", "output": "(∀n: T(n)) ⇒ (∃n: G(n))", "fidelity": 1.0, "ast_type": "ComplexImplication", "category": "logical_reasoning", "generated": true, "complexity": 15}, {"input": "Q ⇒ T", "output": "Q ⇒ T", "fidelity": 1.0, "ast_type": "MaterialImplication", "category": "logical_reasoning", "generated": true, "complexity": 3}, {"input": "∀z ∈ ℤ: G(z)", "output": "∀z ∈ ℤ: G(z)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∀a ∈ ℚ: R(a)", "output": "∀a ∈ ℚ: R(a)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "T ⇒ H", "output": "T ⇒ H", "fidelity": 1.0, "ast_type": "MaterialImplication", "category": "logical_reasoning", "generated": true, "complexity": 3}, {"input": "(∀z: F(z)) ⇒ (∃z: Q(z))", "output": "(∀z: F(z)) ⇒ (∃z: Q(z))", "fidelity": 1.0, "ast_type": "ComplexImplication", "category": "logical_reasoning", "generated": true, "complexity": 15}, {"input": "∀m ∀m: (Greater(m,m) ⇒ Less(m,m))", "output": "∀m ∀m: (Greater(m,m) ⇒ Less(m,m))", "fidelity": 1.0, "ast_type": "UniversalImplication", "category": "logical_reasoning", "generated": true, "complexity": 13}, {"input": "H ⇔ R", "output": "H ⇔ R", "fidelity": 1.0, "ast_type": "LogicalEquivalence", "category": "logical_reasoning", "generated": true, "complexity": 3}, {"input": "T ⇒ F", "output": "T ⇒ F", "fidelity": 1.0, "ast_type": "MaterialImplication", "category": "logical_reasoning", "generated": true, "complexity": 3}, {"input": "¬(G ∧ S)", "output": "¬(G ∧ S)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true, "complexity": 5}, {"input": "¬(R ∨ G)", "output": "¬(R ∨ G)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true, "complexity": 5}, {"input": "P ∧ P ∧ Q", "output": "P ∧ P ∧ Q", "fidelity": 1.0, "ast_type": "LogicalConjunction", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∀y ∀b: (S(y,b) ⇒ Less(y,b))", "output": "∀y ∀b: (S(y,b) ⇒ Less(y,b))", "fidelity": 1.0, "ast_type": "UniversalImplication", "category": "logical_reasoning", "generated": true, "complexity": 13}, {"input": "¬(H ∨ G)", "output": "¬(H ∨ G)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true, "complexity": 5}, {"input": "(∀n: S(n)) ⇒ (∃n: T(n))", "output": "(∀n: S(n)) ⇒ (∃n: T(n))", "fidelity": 1.0, "ast_type": "ComplexImplication", "category": "logical_reasoning", "generated": true, "complexity": 15}, {"input": "H ∨ G ∨ S", "output": "H ∨ G ∨ S", "fidelity": 1.0, "ast_type": "LogicalDisjunction", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∀x ∃c: T(x,c)", "output": "∀x ∃c: T(x,c)", "fidelity": 1.0, "ast_type": "MixedQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∀c ∀n: (Greater(c,n) ⇒ Equal(c,n))", "output": "∀c ∀n: (Greater(c,n) ⇒ Equal(c,n))", "fidelity": 1.0, "ast_type": "UniversalImplication", "category": "logical_reasoning", "generated": true, "complexity": 13}, {"input": "∃z ∈ ℕ: S(z)", "output": "∃z ∈ ℕ: S(z)", "fidelity": 1.0, "ast_type": "ExistentialQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "Q ∧ H ∧ G", "output": "Q ∧ H ∧ G", "fidelity": 1.0, "ast_type": "LogicalConjunction", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "(T ∧ Q) ⇒ T", "output": "(T ∧ Q) ⇒ T", "fidelity": 1.0, "ast_type": "MaterialImplication", "category": "logical_reasoning", "generated": true, "complexity": 8}, {"input": "∀z ∈ ℚ: R(z)", "output": "∀z ∈ ℚ: R(z)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∃c ∈ 𝔹: H(c)", "output": "∃c ∈ 𝔹: H(c)", "fidelity": 1.0, "ast_type": "ExistentialQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "¬(T ∨ F)", "output": "¬(T ∨ F)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true, "complexity": 5}, {"input": "G ∨ F ∨ F", "output": "G ∨ F ∨ F", "fidelity": 1.0, "ast_type": "LogicalDisjunction", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "¬(H ∧ H)", "output": "¬(H ∧ H)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true, "complexity": 5}, {"input": "¬(R ∧ T)", "output": "¬(R ∧ T)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true, "complexity": 5}, {"input": "∃c ∈ ℝ: S(c)", "output": "∃c ∈ ℝ: S(c)", "fidelity": 1.0, "ast_type": "ExistentialQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "¬(S ∨ G)", "output": "¬(S ∨ G)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true, "complexity": 5}, {"input": "¬(Q ∨ F)", "output": "¬(Q ∨ F)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true, "complexity": 5}, {"input": "F ⇔ Q", "output": "F ⇔ Q", "fidelity": 1.0, "ast_type": "LogicalEquivalence", "category": "logical_reasoning", "generated": true, "complexity": 3}, {"input": "∀b ∈ ℝ: R(b)", "output": "∀b ∈ ℝ: R(b)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∀c ∀m: (Less(c,m) ⇒ Greater(c,m))", "output": "∀c ∀m: (Less(c,m) ⇒ Greater(c,m))", "fidelity": 1.0, "ast_type": "UniversalImplication", "category": "logical_reasoning", "generated": true, "complexity": 13}, {"input": "¬(P ∧ F)", "output": "¬(P ∧ F)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true, "complexity": 5}, {"input": "¬(H ∨ P)", "output": "¬(H ∨ P)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true, "complexity": 5}, {"input": "¬(H ∧ S)", "output": "¬(H ∧ S)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true, "complexity": 5}, {"input": "S ⇔ R", "output": "S ⇔ R", "fidelity": 1.0, "ast_type": "LogicalEquivalence", "category": "logical_reasoning", "generated": true, "complexity": 3}, {"input": "∀z ∃n: S(z,n)", "output": "∀z ∃n: S(z,n)", "fidelity": 1.0, "ast_type": "MixedQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∀x ∀y: (Equal(x,y) ⇒ Less(x,y))", "output": "∀x ∀y: (Equal(x,y) ⇒ Less(x,y))", "fidelity": 1.0, "ast_type": "UniversalImplication", "category": "logical_reasoning", "generated": true, "complexity": 13}, {"input": "(∀m: H(m)) ⇒ (∃m: R(m))", "output": "(∀m: H(m)) ⇒ (∃m: R(m))", "fidelity": 1.0, "ast_type": "ComplexImplication", "category": "logical_reasoning", "generated": true, "complexity": 15}, {"input": "H ⇒ P", "output": "H ⇒ P", "fidelity": 1.0, "ast_type": "MaterialImplication", "category": "logical_reasoning", "generated": true, "complexity": 3}, {"input": "(∀y: G(y)) ⇒ (∃y: H(y))", "output": "(∀y: G(y)) ⇒ (∃y: H(y))", "fidelity": 1.0, "ast_type": "ComplexImplication", "category": "logical_reasoning", "generated": true, "complexity": 15}, {"input": "G ⇔ F", "output": "G ⇔ F", "fidelity": 1.0, "ast_type": "LogicalEquivalence", "category": "logical_reasoning", "generated": true, "complexity": 3}, {"input": "¬(P ∧ H)", "output": "¬(P ∧ H)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true, "complexity": 5}, {"input": "∀y ∃x: R(y,x)", "output": "∀y ∃x: R(y,x)", "fidelity": 1.0, "ast_type": "MixedQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "H ∧ H ∧ P", "output": "H ∧ H ∧ P", "fidelity": 1.0, "ast_type": "LogicalConjunction", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "F ⇔ R", "output": "F ⇔ R", "fidelity": 1.0, "ast_type": "LogicalEquivalence", "category": "logical_reasoning", "generated": true, "complexity": 3}, {"input": "¬(P ∨ R)", "output": "¬(P ∨ R)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true, "complexity": 5}, {"input": "∃a ∈ 𝔹: R(a)", "output": "∃a ∈ 𝔹: R(a)", "fidelity": 1.0, "ast_type": "ExistentialQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "S ∧ R ∧ R", "output": "S ∧ R ∧ R", "fidelity": 1.0, "ast_type": "LogicalConjunction", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∃z ∈ ℤ: T(z)", "output": "∃z ∈ ℤ: T(z)", "fidelity": 1.0, "ast_type": "ExistentialQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∃a ∈ 𝔹: R(a)", "output": "∃a ∈ 𝔹: R(a)", "fidelity": 1.0, "ast_type": "ExistentialQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∃y ∈ ℚ: T(y)", "output": "∃y ∈ ℚ: T(y)", "fidelity": 1.0, "ast_type": "ExistentialQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "(Q ∧ R) ⇒ T", "output": "(Q ∧ R) ⇒ T", "fidelity": 1.0, "ast_type": "MaterialImplication", "category": "logical_reasoning", "generated": true, "complexity": 8}, {"input": "H ⇒ R", "output": "H ⇒ R", "fidelity": 1.0, "ast_type": "MaterialImplication", "category": "logical_reasoning", "generated": true, "complexity": 3}, {"input": "F ⇒ H", "output": "F ⇒ H", "fidelity": 1.0, "ast_type": "MaterialImplication", "category": "logical_reasoning", "generated": true, "complexity": 3}, {"input": "¬(G ∧ R)", "output": "¬(G ∧ R)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true, "complexity": 5}, {"input": "Q ∨ Q ∨ G", "output": "Q ∨ Q ∨ G", "fidelity": 1.0, "ast_type": "LogicalDisjunction", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "Q ∨ F ∨ T", "output": "Q ∨ F ∨ T", "fidelity": 1.0, "ast_type": "LogicalDisjunction", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "S ∨ S ∨ R", "output": "S ∨ S ∨ R", "fidelity": 1.0, "ast_type": "LogicalDisjunction", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∀y ∈ 𝔹: S(y)", "output": "∀y ∈ 𝔹: S(y)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∀m ∈ ℤ: F(m)", "output": "∀m ∈ ℤ: F(m)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "F ⇒ Q", "output": "F ⇒ Q", "fidelity": 1.0, "ast_type": "MaterialImplication", "category": "logical_reasoning", "generated": true, "complexity": 3}, {"input": "∃y ∈ ℕ: F(y)", "output": "∃y ∈ ℕ: F(y)", "fidelity": 1.0, "ast_type": "ExistentialQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∀c ∈ ℚ: P(c)", "output": "∀c ∈ ℚ: P(c)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∀b ∀a: (S(b,a) ⇒ Greater(b,a))", "output": "∀b ∀a: (S(b,a) ⇒ Greater(b,a))", "fidelity": 1.0, "ast_type": "UniversalImplication", "category": "logical_reasoning", "generated": true, "complexity": 13}, {"input": "(∀m: G(m)) ⇒ (∃m: F(m))", "output": "(∀m: G(m)) ⇒ (∃m: F(m))", "fidelity": 1.0, "ast_type": "ComplexImplication", "category": "logical_reasoning", "generated": true, "complexity": 15}, {"input": "R ∨ H ∨ T", "output": "R ∨ H ∨ T", "fidelity": 1.0, "ast_type": "LogicalDisjunction", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "¬(R ∨ S)", "output": "¬(R ∨ S)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true, "complexity": 5}, {"input": "F ∨ R ∨ G", "output": "F ∨ R ∨ G", "fidelity": 1.0, "ast_type": "LogicalDisjunction", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∀b ∈ ℤ: Q(b)", "output": "∀b ∈ ℤ: Q(b)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∀y ∀n: (Equal(y,n) ⇒ Greater(y,n))", "output": "∀y ∀n: (Equal(y,n) ⇒ Greater(y,n))", "fidelity": 1.0, "ast_type": "UniversalImplication", "category": "logical_reasoning", "generated": true, "complexity": 13}, {"input": "(∀z: S(z)) ⇒ (∃z: T(z))", "output": "(∀z: S(z)) ⇒ (∃z: T(z))", "fidelity": 1.0, "ast_type": "ComplexImplication", "category": "logical_reasoning", "generated": true, "complexity": 15}, {"input": "¬(T ∧ F)", "output": "¬(T ∧ F)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true, "complexity": 5}, {"input": "¬(T ∨ Q)", "output": "¬(T ∨ Q)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true, "complexity": 5}, {"input": "∀z ∀n: (Equal(z,n) ⇒ S(z,n))", "output": "∀z ∀n: (Equal(z,n) ⇒ S(z,n))", "fidelity": 1.0, "ast_type": "UniversalImplication", "category": "logical_reasoning", "generated": true, "complexity": 13}, {"input": "R ∨ H ∨ F", "output": "R ∨ H ∨ F", "fidelity": 1.0, "ast_type": "LogicalDisjunction", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∀k ∀z: (Less(k,z) ⇒ R(k,z))", "output": "∀k ∀z: (Less(k,z) ⇒ R(k,z))", "fidelity": 1.0, "ast_type": "UniversalImplication", "category": "logical_reasoning", "generated": true, "complexity": 13}, {"input": "P ⇔ Q", "output": "P ⇔ Q", "fidelity": 1.0, "ast_type": "LogicalEquivalence", "category": "logical_reasoning", "generated": true, "complexity": 3}, {"input": "F ∧ T ∧ R", "output": "F ∧ T ∧ R", "fidelity": 1.0, "ast_type": "LogicalConjunction", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "R ∨ S ∨ F", "output": "R ∨ S ∨ F", "fidelity": 1.0, "ast_type": "LogicalDisjunction", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "¬(F ∧ S)", "output": "¬(F ∧ S)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true, "complexity": 5}, {"input": "Q ∨ Q ∨ P", "output": "Q ∨ Q ∨ P", "fidelity": 1.0, "ast_type": "LogicalDisjunction", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "Q ∧ P ∧ R", "output": "Q ∧ P ∧ R", "fidelity": 1.0, "ast_type": "LogicalConjunction", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "P ∧ R ∧ P", "output": "P ∧ R ∧ P", "fidelity": 1.0, "ast_type": "LogicalConjunction", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "(S ∧ P) ⇒ T", "output": "(S ∧ P) ⇒ T", "fidelity": 1.0, "ast_type": "MaterialImplication", "category": "logical_reasoning", "generated": true, "complexity": 8}, {"input": "H ⇒ T", "output": "H ⇒ T", "fidelity": 1.0, "ast_type": "MaterialImplication", "category": "logical_reasoning", "generated": true, "complexity": 3}, {"input": "Q ∧ T ∧ P", "output": "Q ∧ T ∧ P", "fidelity": 1.0, "ast_type": "LogicalConjunction", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "G ⇔ H", "output": "G ⇔ H", "fidelity": 1.0, "ast_type": "LogicalEquivalence", "category": "logical_reasoning", "generated": true, "complexity": 3}, {"input": "∀m ∃b: R(m,b)", "output": "∀m ∃b: R(m,b)", "fidelity": 1.0, "ast_type": "MixedQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "¬(H ∨ H)", "output": "¬(H ∨ H)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true, "complexity": 5}, {"input": "T ∧ R ∧ R", "output": "T ∧ R ∧ R", "fidelity": 1.0, "ast_type": "LogicalConjunction", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∀y ∈ ℝ: H(y)", "output": "∀y ∈ ℝ: H(y)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "F ∨ Q ∨ H", "output": "F ∨ Q ∨ H", "fidelity": 1.0, "ast_type": "LogicalDisjunction", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "T ∧ H ∧ G", "output": "T ∧ H ∧ G", "fidelity": 1.0, "ast_type": "LogicalConjunction", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "¬(S ∧ R)", "output": "¬(S ∧ R)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true, "complexity": 5}, {"input": "H ⇔ S", "output": "H ⇔ S", "fidelity": 1.0, "ast_type": "LogicalEquivalence", "category": "logical_reasoning", "generated": true, "complexity": 3}, {"input": "∃n ∈ 𝔹: G(n)", "output": "∃n ∈ 𝔹: G(n)", "fidelity": 1.0, "ast_type": "ExistentialQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "¬(T ∨ G)", "output": "¬(T ∨ G)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true, "complexity": 5}, {"input": "¬(S ∨ R)", "output": "¬(S ∨ R)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true, "complexity": 5}, {"input": "∀x ∀m: (R(x,m) ⇒ S(x,m))", "output": "∀x ∀m: (R(x,m) ⇒ S(x,m))", "fidelity": 1.0, "ast_type": "UniversalImplication", "category": "logical_reasoning", "generated": true, "complexity": 13}, {"input": "(H ∧ R) ⇒ T", "output": "(H ∧ R) ⇒ T", "fidelity": 1.0, "ast_type": "MaterialImplication", "category": "logical_reasoning", "generated": true, "complexity": 8}, {"input": "P ∧ S ∧ S", "output": "P ∧ S ∧ S", "fidelity": 1.0, "ast_type": "LogicalConjunction", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∀c ∈ ℤ: P(c)", "output": "∀c ∈ ℤ: P(c)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∃b ∈ 𝔹: R(b)", "output": "∃b ∈ 𝔹: R(b)", "fidelity": 1.0, "ast_type": "ExistentialQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "¬(Q ∧ F)", "output": "¬(Q ∧ F)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true, "complexity": 5}, {"input": "∀a ∃z: Equal(a,z)", "output": "∀a ∃z: Equal(a,z)", "fidelity": 1.0, "ast_type": "MixedQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "(G ∧ S) ⇒ P", "output": "(G ∧ S) ⇒ P", "fidelity": 1.0, "ast_type": "MaterialImplication", "category": "logical_reasoning", "generated": true, "complexity": 8}, {"input": "¬(Q ∧ P)", "output": "¬(Q ∧ P)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true, "complexity": 5}, {"input": "Q ⇔ P", "output": "Q ⇔ P", "fidelity": 1.0, "ast_type": "LogicalEquivalence", "category": "logical_reasoning", "generated": true, "complexity": 3}, {"input": "T ∧ F ∧ F", "output": "T ∧ F ∧ F", "fidelity": 1.0, "ast_type": "LogicalConjunction", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∀z ∈ ℤ: Q(z)", "output": "∀z ∈ ℤ: Q(z)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∀m ∈ 𝔹: G(m)", "output": "∀m ∈ 𝔹: G(m)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "Q ⇔ H", "output": "Q ⇔ H", "fidelity": 1.0, "ast_type": "LogicalEquivalence", "category": "logical_reasoning", "generated": true, "complexity": 3}, {"input": "P ⇒ H", "output": "P ⇒ H", "fidelity": 1.0, "ast_type": "MaterialImplication", "category": "logical_reasoning", "generated": true, "complexity": 3}, {"input": "¬(R ∨ P)", "output": "¬(R ∨ P)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true, "complexity": 5}, {"input": "¬(T ∧ F)", "output": "¬(T ∧ F)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true, "complexity": 5}, {"input": "∃m ∈ 𝔹: H(m)", "output": "∃m ∈ 𝔹: H(m)", "fidelity": 1.0, "ast_type": "ExistentialQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∀m ∈ ℝ: R(m)", "output": "∀m ∈ ℝ: R(m)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∀n ∈ ℚ: R(n)", "output": "∀n ∈ ℚ: R(n)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "¬(F ∧ Q)", "output": "¬(F ∧ Q)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true, "complexity": 5}, {"input": "∀m ∃c: T(m,c)", "output": "∀m ∃c: T(m,c)", "fidelity": 1.0, "ast_type": "MixedQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∃a ∈ ℤ: G(a)", "output": "∃a ∈ ℤ: G(a)", "fidelity": 1.0, "ast_type": "ExistentialQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∀m ∀k: (Greater(m,k) ⇒ R(m,k))", "output": "∀m ∀k: (Greater(m,k) ⇒ R(m,k))", "fidelity": 1.0, "ast_type": "UniversalImplication", "category": "logical_reasoning", "generated": true, "complexity": 13}, {"input": "∀n ∃z: Greater(n,z)", "output": "∀n ∃z: Greater(n,z)", "fidelity": 1.0, "ast_type": "MixedQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∃z ∈ ℚ: S(z)", "output": "∃z ∈ ℚ: S(z)", "fidelity": 1.0, "ast_type": "ExistentialQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "¬(Q ∨ G)", "output": "¬(Q ∨ G)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true, "complexity": 5}, {"input": "∀m ∈ ℚ: G(m)", "output": "∀m ∈ ℚ: G(m)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "G ⇔ S", "output": "G ⇔ S", "fidelity": 1.0, "ast_type": "LogicalEquivalence", "category": "logical_reasoning", "generated": true, "complexity": 3}, {"input": "∃y ∈ ℤ: T(y)", "output": "∃y ∈ ℤ: T(y)", "fidelity": 1.0, "ast_type": "ExistentialQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "F ⇒ R", "output": "F ⇒ R", "fidelity": 1.0, "ast_type": "MaterialImplication", "category": "logical_reasoning", "generated": true, "complexity": 3}, {"input": "∃m ∈ ℝ: H(m)", "output": "∃m ∈ ℝ: H(m)", "fidelity": 1.0, "ast_type": "ExistentialQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "(∀n: P(n)) ⇒ (∃n: R(n))", "output": "(∀n: P(n)) ⇒ (∃n: R(n))", "fidelity": 1.0, "ast_type": "ComplexImplication", "category": "logical_reasoning", "generated": true, "complexity": 15}, {"input": "∀z ∈ ℝ: H(z)", "output": "∀z ∈ ℝ: H(z)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "(∀b: S(b)) ⇒ (∃b: S(b))", "output": "(∀b: S(b)) ⇒ (∃b: S(b))", "fidelity": 1.0, "ast_type": "ComplexImplication", "category": "logical_reasoning", "generated": true, "complexity": 15}, {"input": "∀n ∃y: Less(n,y)", "output": "∀n ∃y: Less(n,y)", "fidelity": 1.0, "ast_type": "MixedQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∃n ∈ ℕ: P(n)", "output": "∃n ∈ ℕ: P(n)", "fidelity": 1.0, "ast_type": "ExistentialQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "T ∧ H ∧ R", "output": "T ∧ H ∧ R", "fidelity": 1.0, "ast_type": "LogicalConjunction", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∀m ∃z: T(m,z)", "output": "∀m ∃z: T(m,z)", "fidelity": 1.0, "ast_type": "MixedQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "H ⇔ T", "output": "H ⇔ T", "fidelity": 1.0, "ast_type": "LogicalEquivalence", "category": "logical_reasoning", "generated": true, "complexity": 3}, {"input": "G ∨ G ∨ H", "output": "G ∨ G ∨ H", "fidelity": 1.0, "ast_type": "LogicalDisjunction", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "(∀k: P(k)) ⇒ (∃k: H(k))", "output": "(∀k: P(k)) ⇒ (∃k: H(k))", "fidelity": 1.0, "ast_type": "ComplexImplication", "category": "logical_reasoning", "generated": true, "complexity": 15}, {"input": "∀k ∃z: T(k,z)", "output": "∀k ∃z: T(k,z)", "fidelity": 1.0, "ast_type": "MixedQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∀n ∃c: R(n,c)", "output": "∀n ∃c: R(n,c)", "fidelity": 1.0, "ast_type": "MixedQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "(∀b: G(b)) ⇒ (∃b: F(b))", "output": "(∀b: G(b)) ⇒ (∃b: F(b))", "fidelity": 1.0, "ast_type": "ComplexImplication", "category": "logical_reasoning", "generated": true, "complexity": 15}, {"input": "(∀b: H(b)) ⇒ (∃b: H(b))", "output": "(∀b: H(b)) ⇒ (∃b: H(b))", "fidelity": 1.0, "ast_type": "ComplexImplication", "category": "logical_reasoning", "generated": true, "complexity": 15}, {"input": "(Q ∧ P) ⇒ P", "output": "(Q ∧ P) ⇒ P", "fidelity": 1.0, "ast_type": "MaterialImplication", "category": "logical_reasoning", "generated": true, "complexity": 8}, {"input": "¬(F ∨ Q)", "output": "¬(F ∨ Q)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true, "complexity": 5}, {"input": "∀k ∈ ℚ: H(k)", "output": "∀k ∈ ℚ: H(k)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∀c ∀k: (T(c,k) ⇒ T(c,k))", "output": "∀c ∀k: (T(c,k) ⇒ T(c,k))", "fidelity": 1.0, "ast_type": "UniversalImplication", "category": "logical_reasoning", "generated": true, "complexity": 13}, {"input": "(P ∧ P) ⇒ G", "output": "(P ∧ P) ⇒ G", "fidelity": 1.0, "ast_type": "MaterialImplication", "category": "logical_reasoning", "generated": true, "complexity": 8}, {"input": "R ⇒ G", "output": "R ⇒ G", "fidelity": 1.0, "ast_type": "MaterialImplication", "category": "logical_reasoning", "generated": true, "complexity": 3}, {"input": "(∀y: Q(y)) ⇒ (∃y: F(y))", "output": "(∀y: Q(y)) ⇒ (∃y: F(y))", "fidelity": 1.0, "ast_type": "ComplexImplication", "category": "logical_reasoning", "generated": true, "complexity": 15}, {"input": "∀z ∈ 𝔹: P(z)", "output": "∀z ∈ 𝔹: P(z)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∀n ∀k: (Greater(n,k) ⇒ R(n,k))", "output": "∀n ∀k: (Greater(n,k) ⇒ R(n,k))", "fidelity": 1.0, "ast_type": "UniversalImplication", "category": "logical_reasoning", "generated": true, "complexity": 13}, {"input": "F ⇒ S", "output": "F ⇒ S", "fidelity": 1.0, "ast_type": "MaterialImplication", "category": "logical_reasoning", "generated": true, "complexity": 3}, {"input": "∀z ∃x: T(z,x)", "output": "∀z ∃x: T(z,x)", "fidelity": 1.0, "ast_type": "MixedQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∀z ∈ ℤ: T(z)", "output": "∀z ∈ ℤ: T(z)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "(∀x: P(x)) ⇒ (∃x: S(x))", "output": "(∀x: P(x)) ⇒ (∃x: S(x))", "fidelity": 1.0, "ast_type": "ComplexImplication", "category": "logical_reasoning", "generated": true, "complexity": 15}, {"input": "∀m ∈ ℚ: T(m)", "output": "∀m ∈ ℚ: T(m)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "Q ∧ G ∧ F", "output": "Q ∧ G ∧ F", "fidelity": 1.0, "ast_type": "LogicalConjunction", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∀c ∈ ℤ: H(c)", "output": "∀c ∈ ℤ: H(c)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∃a ∈ ℤ: F(a)", "output": "∃a ∈ ℤ: F(a)", "fidelity": 1.0, "ast_type": "ExistentialQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "Q ⇔ T", "output": "Q ⇔ T", "fidelity": 1.0, "ast_type": "LogicalEquivalence", "category": "logical_reasoning", "generated": true, "complexity": 3}, {"input": "∀a ∈ ℚ: S(a)", "output": "∀a ∈ ℚ: S(a)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "H ⇔ H", "output": "H ⇔ H", "fidelity": 1.0, "ast_type": "LogicalEquivalence", "category": "logical_reasoning", "generated": true, "complexity": 3}, {"input": "∀y ∃b: S(y,b)", "output": "∀y ∃b: S(y,b)", "fidelity": 1.0, "ast_type": "MixedQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "(∀y: R(y)) ⇒ (∃y: G(y))", "output": "(∀y: R(y)) ⇒ (∃y: G(y))", "fidelity": 1.0, "ast_type": "ComplexImplication", "category": "logical_reasoning", "generated": true, "complexity": 15}, {"input": "∀n ∃n: Greater(n,n)", "output": "∀n ∃n: Greater(n,n)", "fidelity": 1.0, "ast_type": "MixedQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∀c ∈ 𝔹: R(c)", "output": "∀c ∈ 𝔹: R(c)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "G ⇔ F", "output": "G ⇔ F", "fidelity": 1.0, "ast_type": "LogicalEquivalence", "category": "logical_reasoning", "generated": true, "complexity": 3}, {"input": "R ⇔ S", "output": "R ⇔ S", "fidelity": 1.0, "ast_type": "LogicalEquivalence", "category": "logical_reasoning", "generated": true, "complexity": 3}, {"input": "∃m ∈ 𝔹: T(m)", "output": "∃m ∈ 𝔹: T(m)", "fidelity": 1.0, "ast_type": "ExistentialQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "Q ∨ Q ∨ T", "output": "Q ∨ Q ∨ T", "fidelity": 1.0, "ast_type": "LogicalDisjunction", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "G ∧ S ∧ P", "output": "G ∧ S ∧ P", "fidelity": 1.0, "ast_type": "LogicalConjunction", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∀y ∃y: T(y,y)", "output": "∀y ∃y: T(y,y)", "fidelity": 1.0, "ast_type": "MixedQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "G ⇒ S", "output": "G ⇒ S", "fidelity": 1.0, "ast_type": "MaterialImplication", "category": "logical_reasoning", "generated": true, "complexity": 3}, {"input": "(∀c: S(c)) ⇒ (∃c: S(c))", "output": "(∀c: S(c)) ⇒ (∃c: S(c))", "fidelity": 1.0, "ast_type": "ComplexImplication", "category": "logical_reasoning", "generated": true, "complexity": 15}, {"input": "¬(Q ∧ H)", "output": "¬(Q ∧ H)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true, "complexity": 5}, {"input": "∀n ∃c: Less(n,c)", "output": "∀n ∃c: Less(n,c)", "fidelity": 1.0, "ast_type": "MixedQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "R ∧ G ∧ H", "output": "R ∧ G ∧ H", "fidelity": 1.0, "ast_type": "LogicalConjunction", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∀b ∃b: Greater(b,b)", "output": "∀b ∃b: Greater(b,b)", "fidelity": 1.0, "ast_type": "MixedQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "¬(H ∨ S)", "output": "¬(H ∨ S)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true, "complexity": 5}, {"input": "∀x ∃k: S(x,k)", "output": "∀x ∃k: S(x,k)", "fidelity": 1.0, "ast_type": "MixedQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "H ∨ F ∨ H", "output": "H ∨ F ∨ H", "fidelity": 1.0, "ast_type": "LogicalDisjunction", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "¬(R ∧ S)", "output": "¬(R ∧ S)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true, "complexity": 5}, {"input": "G ∧ R ∧ Q", "output": "G ∧ R ∧ Q", "fidelity": 1.0, "ast_type": "LogicalConjunction", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "T ⇔ T", "output": "T ⇔ T", "fidelity": 1.0, "ast_type": "LogicalEquivalence", "category": "logical_reasoning", "generated": true, "complexity": 3}, {"input": "¬(S ∨ H)", "output": "¬(S ∨ H)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true, "complexity": 5}, {"input": "∀m ∈ ℝ: H(m)", "output": "∀m ∈ ℝ: H(m)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "(∀n: Q(n)) ⇒ (∃n: P(n))", "output": "(∀n: Q(n)) ⇒ (∃n: P(n))", "fidelity": 1.0, "ast_type": "ComplexImplication", "category": "logical_reasoning", "generated": true, "complexity": 15}, {"input": "G ⇔ H", "output": "G ⇔ H", "fidelity": 1.0, "ast_type": "LogicalEquivalence", "category": "logical_reasoning", "generated": true, "complexity": 3}, {"input": "¬(Q ∨ G)", "output": "¬(Q ∨ G)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true, "complexity": 5}, {"input": "∀m ∀b: (Greater(m,b) ⇒ R(m,b))", "output": "∀m ∀b: (Greater(m,b) ⇒ R(m,b))", "fidelity": 1.0, "ast_type": "UniversalImplication", "category": "logical_reasoning", "generated": true, "complexity": 13}, {"input": "∀n ∃m: Equal(n,m)", "output": "∀n ∃m: Equal(n,m)", "fidelity": 1.0, "ast_type": "MixedQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∀m ∀k: (Greater(m,k) ⇒ Equal(m,k))", "output": "∀m ∀k: (Greater(m,k) ⇒ Equal(m,k))", "fidelity": 1.0, "ast_type": "UniversalImplication", "category": "logical_reasoning", "generated": true, "complexity": 13}, {"input": "T ⇔ F", "output": "T ⇔ F", "fidelity": 1.0, "ast_type": "LogicalEquivalence", "category": "logical_reasoning", "generated": true, "complexity": 3}, {"input": "¬(R ∨ F)", "output": "¬(R ∨ F)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true, "complexity": 5}, {"input": "(H ∧ P) ⇒ F", "output": "(H ∧ P) ⇒ F", "fidelity": 1.0, "ast_type": "MaterialImplication", "category": "logical_reasoning", "generated": true, "complexity": 8}, {"input": "∀c ∀z: (R(c,z) ⇒ T(c,z))", "output": "∀c ∀z: (R(c,z) ⇒ T(c,z))", "fidelity": 1.0, "ast_type": "UniversalImplication", "category": "logical_reasoning", "generated": true, "complexity": 13}, {"input": "∃m ∈ 𝔹: Q(m)", "output": "∃m ∈ 𝔹: Q(m)", "fidelity": 1.0, "ast_type": "ExistentialQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "(∀a: S(a)) ⇒ (∃a: G(a))", "output": "(∀a: S(a)) ⇒ (∃a: G(a))", "fidelity": 1.0, "ast_type": "ComplexImplication", "category": "logical_reasoning", "generated": true, "complexity": 15}, {"input": "¬(S ∧ H)", "output": "¬(S ∧ H)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true, "complexity": 5}, {"input": "¬(S ∧ F)", "output": "¬(S ∧ F)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true, "complexity": 5}, {"input": "¬(H ∧ S)", "output": "¬(H ∧ S)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true, "complexity": 5}, {"input": "P ∧ G ∧ Q", "output": "P ∧ G ∧ Q", "fidelity": 1.0, "ast_type": "LogicalConjunction", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∀b ∀b: (R(b,b) ⇒ Less(b,b))", "output": "∀b ∀b: (R(b,b) ⇒ Less(b,b))", "fidelity": 1.0, "ast_type": "UniversalImplication", "category": "logical_reasoning", "generated": true, "complexity": 13}, {"input": "R ⇔ S", "output": "R ⇔ S", "fidelity": 1.0, "ast_type": "LogicalEquivalence", "category": "logical_reasoning", "generated": true, "complexity": 3}, {"input": "∀k ∀x: (R(k,x) ⇒ R(k,x))", "output": "∀k ∀x: (R(k,x) ⇒ R(k,x))", "fidelity": 1.0, "ast_type": "UniversalImplication", "category": "logical_reasoning", "generated": true, "complexity": 13}, {"input": "(P ∧ T) ⇒ P", "output": "(P ∧ T) ⇒ P", "fidelity": 1.0, "ast_type": "MaterialImplication", "category": "logical_reasoning", "generated": true, "complexity": 8}, {"input": "∀a ∃k: Equal(a,k)", "output": "∀a ∃k: Equal(a,k)", "fidelity": 1.0, "ast_type": "MixedQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "¬(F ∧ Q)", "output": "¬(F ∧ Q)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true, "complexity": 5}, {"input": "T ⇒ S", "output": "T ⇒ S", "fidelity": 1.0, "ast_type": "MaterialImplication", "category": "logical_reasoning", "generated": true, "complexity": 3}, {"input": "¬(Q ∨ T)", "output": "¬(Q ∨ T)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true, "complexity": 5}, {"input": "(∀c: S(c)) ⇒ (∃c: F(c))", "output": "(∀c: S(c)) ⇒ (∃c: F(c))", "fidelity": 1.0, "ast_type": "ComplexImplication", "category": "logical_reasoning", "generated": true, "complexity": 15}, {"input": "¬(Q ∨ T)", "output": "¬(Q ∨ T)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true, "complexity": 5}, {"input": "¬(F ∧ H)", "output": "¬(F ∧ H)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true, "complexity": 5}, {"input": "∀n ∈ ℝ: R(n)", "output": "∀n ∈ ℝ: R(n)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "(∀b: G(b)) ⇒ (∃b: S(b))", "output": "(∀b: G(b)) ⇒ (∃b: S(b))", "fidelity": 1.0, "ast_type": "ComplexImplication", "category": "logical_reasoning", "generated": true, "complexity": 15}, {"input": "∀m ∀b: (R(m,b) ⇒ S(m,b))", "output": "∀m ∀b: (R(m,b) ⇒ S(m,b))", "fidelity": 1.0, "ast_type": "UniversalImplication", "category": "logical_reasoning", "generated": true, "complexity": 13}, {"input": "∀k ∈ ℚ: Q(k)", "output": "∀k ∈ ℚ: Q(k)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "∀b ∈ ℤ: F(b)", "output": "∀b ∈ ℤ: F(b)", "fidelity": 1.0, "ast_type": "UniversalQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "(G ∧ P) ⇒ F", "output": "(G ∧ P) ⇒ F", "fidelity": 1.0, "ast_type": "MaterialImplication", "category": "logical_reasoning", "generated": true, "complexity": 8}, {"input": "G ∧ F ∧ H", "output": "G ∧ F ∧ H", "fidelity": 1.0, "ast_type": "LogicalConjunction", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "(∀x: H(x)) ⇒ (∃x: H(x))", "output": "(∀x: H(x)) ⇒ (∃x: H(x))", "fidelity": 1.0, "ast_type": "ComplexImplication", "category": "logical_reasoning", "generated": true, "complexity": 15}, {"input": "S ⇔ G", "output": "S ⇔ G", "fidelity": 1.0, "ast_type": "LogicalEquivalence", "category": "logical_reasoning", "generated": true, "complexity": 3}, {"input": "¬(G ∧ R)", "output": "¬(G ∧ R)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true, "complexity": 5}, {"input": "∃y ∈ ℤ: Q(y)", "output": "∃y ∈ ℤ: Q(y)", "fidelity": 1.0, "ast_type": "ExistentialQuantification", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "¬(F ∨ H)", "output": "¬(F ∨ H)", "fidelity": 1.0, "ast_type": "LogicalNegation", "category": "logical_reasoning", "generated": true, "complexity": 5}, {"input": "T ∧ S ∧ P", "output": "T ∧ S ∧ P", "fidelity": 1.0, "ast_type": "LogicalConjunction", "category": "logical_reasoning", "generated": true, "complexity": 6}, {"input": "Even(x) ⇒ DivisibleBy2(x) | DivisibleBy2(x) ⇒ Integer(x) | Integer(x) ⇒ Real(x) | Even(6) | ⊢ Real(6)", "output": "Even(x) ⇒ DivisibleBy2(x) | DivisibleBy2(x) ⇒ Integer(x) | Integer(x) ⇒ Real(x) | Even(6) | ⊢ Real(6)", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 5, "reasoning_steps": 3}, {"input": "S ⇒ Q | Q ⇒ F | S | ⊢ F", "output": "S ⇒ Q | Q ⇒ F | S | ⊢ F", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 4, "reasoning_steps": 2}, {"input": "G ⇒ F | F ⇒ R | R ⇒ Q | Q ⇒ T | G | ⊢ T", "output": "G ⇒ F | F ⇒ R | R ⇒ Q | Q ⇒ T | G | ⊢ T", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 6, "reasoning_steps": 4}, {"input": "P ⇒ Q | Q ⇒ S | S ⇒ H | H ⇒ R | P | ⊢ R", "output": "P ⇒ Q | Q ⇒ S | S ⇒ H | H ⇒ R | P | ⊢ R", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 6, "reasoning_steps": 4}, {"input": "S ⇒ P | P ⇒ H | S | ⊢ H", "output": "S ⇒ P | P ⇒ H | S | ⊢ H", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 4, "reasoning_steps": 2}, {"input": "S ⇒ Q | Q ⇒ F | S | ⊢ F", "output": "S ⇒ Q | Q ⇒ F | S | ⊢ F", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 4, "reasoning_steps": 2}, {"input": "Even(y) ⇒ DivisibleBy2(y) | DivisibleBy2(y) ⇒ Integer(y) | Integer(y) ⇒ Real(y) | Even(10) | ⊢ Real(10)", "output": "Even(y) ⇒ DivisibleBy2(y) | DivisibleBy2(y) ⇒ Integer(y) | Integer(y) ⇒ Real(y) | Even(10) | ⊢ Real(10)", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 5, "reasoning_steps": 3}, {"input": "G ⇒ H | H ⇒ F | F ⇒ P | P ⇒ Q | G | ⊢ Q", "output": "G ⇒ H | H ⇒ F | F ⇒ P | P ⇒ Q | G | ⊢ Q", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 6, "reasoning_steps": 4}, {"input": "F ⇒ G | G ⇒ H | F | ⊢ H", "output": "F ⇒ G | G ⇒ H | F | ⊢ H", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 4, "reasoning_steps": 2}, {"input": "P ⇒ F | F ⇒ Q | P | ⊢ Q", "output": "P ⇒ F | F ⇒ Q | P | ⊢ Q", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 4, "reasoning_steps": 2}, {"input": "Q ⇒ F | F ⇒ S | S ⇒ R | R ⇒ T | Q | ⊢ T", "output": "Q ⇒ F | F ⇒ S | S ⇒ R | R ⇒ T | Q | ⊢ T", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 6, "reasoning_steps": 4}, {"input": "Even(y) ⇒ DivisibleBy2(y) | DivisibleBy2(y) ⇒ Integer(y) | Integer(y) ⇒ Real(y) | Even(8) | ⊢ Real(8)", "output": "Even(y) ⇒ DivisibleBy2(y) | DivisibleBy2(y) ⇒ Integer(y) | Integer(y) ⇒ Real(y) | Even(8) | ⊢ Real(8)", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 5, "reasoning_steps": 3}, {"input": "P ⇒ Q | Q ⇒ R | P | ⊢ R", "output": "P ⇒ Q | Q ⇒ R | P | ⊢ R", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 4, "reasoning_steps": 2}, {"input": "Even(z) ⇒ DivisibleBy2(z) | DivisibleBy2(z) ⇒ Integer(z) | Integer(z) ⇒ Real(z) | Even(8) | ⊢ Real(8)", "output": "Even(z) ⇒ DivisibleBy2(z) | DivisibleBy2(z) ⇒ Integer(z) | Integer(z) ⇒ Real(z) | Even(8) | ⊢ Real(8)", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 5, "reasoning_steps": 3}, {"input": "Even(y) ⇒ DivisibleBy2(y) | DivisibleBy2(y) ⇒ Integer(y) | Integer(y) ⇒ Real(y) | Even(8) | ⊢ Real(8)", "output": "Even(y) ⇒ DivisibleBy2(y) | DivisibleBy2(y) ⇒ Integer(y) | Integer(y) ⇒ Real(y) | Even(8) | ⊢ Real(8)", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 5, "reasoning_steps": 3}, {"input": "S ⇒ Q | Q ⇒ G | S | ⊢ G", "output": "S ⇒ Q | Q ⇒ G | S | ⊢ G", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 4, "reasoning_steps": 2}, {"input": "Q ⇒ P | P ⇒ T | Q | ⊢ T", "output": "Q ⇒ P | P ⇒ T | Q | ⊢ T", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 4, "reasoning_steps": 2}, {"input": "G ⇒ F | F ⇒ S | G | ⊢ S", "output": "G ⇒ F | F ⇒ S | G | ⊢ S", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 4, "reasoning_steps": 2}, {"input": "Even(x) ⇒ DivisibleBy2(x) | DivisibleBy2(x) ⇒ Integer(x) | Integer(x) ⇒ Real(x) | Even(6) | ⊢ Real(6)", "output": "Even(x) ⇒ DivisibleBy2(x) | DivisibleBy2(x) ⇒ Integer(x) | Integer(x) ⇒ Real(x) | Even(6) | ⊢ Real(6)", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 5, "reasoning_steps": 3}, {"input": "G ⇒ T | T ⇒ P | P ⇒ Q | Q ⇒ S | G | ⊢ S", "output": "G ⇒ T | T ⇒ P | P ⇒ Q | Q ⇒ S | G | ⊢ S", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 6, "reasoning_steps": 4}, {"input": "Even(y) ⇒ DivisibleBy2(y) | DivisibleBy2(y) ⇒ Integer(y) | Integer(y) ⇒ Real(y) | Even(12) | ⊢ Real(12)", "output": "Even(y) ⇒ DivisibleBy2(y) | DivisibleBy2(y) ⇒ Integer(y) | Integer(y) ⇒ Real(y) | Even(12) | ⊢ Real(12)", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 5, "reasoning_steps": 3}, {"input": "S ⇒ Q | Q ⇒ H | S | ⊢ H", "output": "S ⇒ Q | Q ⇒ H | S | ⊢ H", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 4, "reasoning_steps": 2}, {"input": "Even(x) ⇒ DivisibleBy2(x) | DivisibleBy2(x) ⇒ Integer(x) | Integer(x) ⇒ Real(x) | Even(8) | ⊢ Real(8)", "output": "Even(x) ⇒ DivisibleBy2(x) | DivisibleBy2(x) ⇒ Integer(x) | Integer(x) ⇒ Real(x) | Even(8) | ⊢ Real(8)", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 5, "reasoning_steps": 3}, {"input": "G ⇒ Q | Q ⇒ F | G | ⊢ F", "output": "G ⇒ Q | Q ⇒ F | G | ⊢ F", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 4, "reasoning_steps": 2}, {"input": "G ⇒ F | F ⇒ Q | Q ⇒ T | T ⇒ P | G | ⊢ P", "output": "G ⇒ F | F ⇒ Q | Q ⇒ T | T ⇒ P | G | ⊢ P", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 6, "reasoning_steps": 4}, {"input": "Even(y) ⇒ DivisibleBy2(y) | DivisibleBy2(y) ⇒ Integer(y) | Integer(y) ⇒ Real(y) | Even(8) | ⊢ Real(8)", "output": "Even(y) ⇒ DivisibleBy2(y) | DivisibleBy2(y) ⇒ Integer(y) | Integer(y) ⇒ Real(y) | Even(8) | ⊢ Real(8)", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 5, "reasoning_steps": 3}, {"input": "Even(x) ⇒ DivisibleBy2(x) | DivisibleBy2(x) ⇒ Integer(x) | Integer(x) ⇒ Real(x) | Even(12) | ⊢ Real(12)", "output": "Even(x) ⇒ DivisibleBy2(x) | DivisibleBy2(x) ⇒ Integer(x) | Integer(x) ⇒ Real(x) | Even(12) | ⊢ Real(12)", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 5, "reasoning_steps": 3}, {"input": "H ⇒ Q | Q ⇒ S | S ⇒ F | F ⇒ P | H | ⊢ P", "output": "H ⇒ Q | Q ⇒ S | S ⇒ F | F ⇒ P | H | ⊢ P", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 6, "reasoning_steps": 4}, {"input": "Q ⇒ P | P ⇒ S | Q | ⊢ S", "output": "Q ⇒ P | P ⇒ S | Q | ⊢ S", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 4, "reasoning_steps": 2}, {"input": "P ⇒ G | G ⇒ Q | P | ⊢ Q", "output": "P ⇒ G | G ⇒ Q | P | ⊢ Q", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 4, "reasoning_steps": 2}, {"input": "Even(z) ⇒ DivisibleBy2(z) | DivisibleBy2(z) ⇒ Integer(z) | Integer(z) ⇒ Real(z) | Even(8) | ⊢ Real(8)", "output": "Even(z) ⇒ DivisibleBy2(z) | DivisibleBy2(z) ⇒ Integer(z) | Integer(z) ⇒ Real(z) | Even(8) | ⊢ Real(8)", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 5, "reasoning_steps": 3}, {"input": "T ⇒ Q | Q ⇒ H | T | ⊢ H", "output": "T ⇒ Q | Q ⇒ H | T | ⊢ H", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 4, "reasoning_steps": 2}, {"input": "H ⇒ F | F ⇒ P | P ⇒ S | S ⇒ T | H | ⊢ T", "output": "H ⇒ F | F ⇒ P | P ⇒ S | S ⇒ T | H | ⊢ T", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 6, "reasoning_steps": 4}, {"input": "G ⇒ P | P ⇒ H | H ⇒ Q | Q ⇒ T | G | ⊢ T", "output": "G ⇒ P | P ⇒ H | H ⇒ Q | Q ⇒ T | G | ⊢ T", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 6, "reasoning_steps": 4}, {"input": "R ⇒ T | T ⇒ G | G ⇒ Q | Q ⇒ F | R | ⊢ F", "output": "R ⇒ T | T ⇒ G | G ⇒ Q | Q ⇒ F | R | ⊢ F", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 6, "reasoning_steps": 4}, {"input": "P ⇒ S | S ⇒ Q | P | ⊢ Q", "output": "P ⇒ S | S ⇒ Q | P | ⊢ Q", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 4, "reasoning_steps": 2}, {"input": "G ⇒ S | S ⇒ T | T ⇒ R | R ⇒ H | G | ⊢ H", "output": "G ⇒ S | S ⇒ T | T ⇒ R | R ⇒ H | G | ⊢ H", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 6, "reasoning_steps": 4}, {"input": "Even(y) ⇒ DivisibleBy2(y) | DivisibleBy2(y) ⇒ Integer(y) | Integer(y) ⇒ Real(y) | Even(12) | ⊢ Real(12)", "output": "Even(y) ⇒ DivisibleBy2(y) | DivisibleBy2(y) ⇒ Integer(y) | Integer(y) ⇒ Real(y) | Even(12) | ⊢ Real(12)", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 5, "reasoning_steps": 3}, {"input": "P ⇒ S | S ⇒ Q | P | ⊢ Q", "output": "P ⇒ S | S ⇒ Q | P | ⊢ Q", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 4, "reasoning_steps": 2}, {"input": "F ⇒ P | P ⇒ Q | F | ⊢ Q", "output": "F ⇒ P | P ⇒ Q | F | ⊢ Q", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 4, "reasoning_steps": 2}, {"input": "G ⇒ Q | Q ⇒ S | G | ⊢ S", "output": "G ⇒ Q | Q ⇒ S | G | ⊢ S", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 4, "reasoning_steps": 2}, {"input": "R ⇒ F | F ⇒ S | S ⇒ Q | Q ⇒ T | R | ⊢ T", "output": "R ⇒ F | F ⇒ S | S ⇒ Q | Q ⇒ T | R | ⊢ T", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 6, "reasoning_steps": 4}, {"input": "T ⇒ H | H ⇒ S | T | ⊢ S", "output": "T ⇒ H | H ⇒ S | T | ⊢ S", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 4, "reasoning_steps": 2}, {"input": "Even(y) ⇒ DivisibleBy2(y) | DivisibleBy2(y) ⇒ Integer(y) | Integer(y) ⇒ Real(y) | Even(12) | ⊢ Real(12)", "output": "Even(y) ⇒ DivisibleBy2(y) | DivisibleBy2(y) ⇒ Integer(y) | Integer(y) ⇒ Real(y) | Even(12) | ⊢ Real(12)", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 5, "reasoning_steps": 3}, {"input": "Even(x) ⇒ DivisibleBy2(x) | DivisibleBy2(x) ⇒ Integer(x) | Integer(x) ⇒ Real(x) | Even(8) | ⊢ Real(8)", "output": "Even(x) ⇒ DivisibleBy2(x) | DivisibleBy2(x) ⇒ Integer(x) | Integer(x) ⇒ Real(x) | Even(8) | ⊢ Real(8)", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 5, "reasoning_steps": 3}, {"input": "S ⇒ H | H ⇒ P | P ⇒ Q | Q ⇒ F | S | ⊢ F", "output": "S ⇒ H | H ⇒ P | P ⇒ Q | Q ⇒ F | S | ⊢ F", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 6, "reasoning_steps": 4}, {"input": "Even(y) ⇒ DivisibleBy2(y) | DivisibleBy2(y) ⇒ Integer(y) | Integer(y) ⇒ Real(y) | Even(6) | ⊢ Real(6)", "output": "Even(y) ⇒ DivisibleBy2(y) | DivisibleBy2(y) ⇒ Integer(y) | Integer(y) ⇒ Real(y) | Even(6) | ⊢ Real(6)", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 5, "reasoning_steps": 3}, {"input": "Even(y) ⇒ DivisibleBy2(y) | DivisibleBy2(y) ⇒ Integer(y) | Integer(y) ⇒ Real(y) | Even(10) | ⊢ Real(10)", "output": "Even(y) ⇒ DivisibleBy2(y) | DivisibleBy2(y) ⇒ Integer(y) | Integer(y) ⇒ Real(y) | Even(10) | ⊢ Real(10)", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 5, "reasoning_steps": 3}, {"input": "R ⇒ H | H ⇒ P | R | ⊢ P", "output": "R ⇒ H | H ⇒ P | R | ⊢ P", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 4, "reasoning_steps": 2}, {"input": "Q ⇒ H | H ⇒ F | F ⇒ T | T ⇒ G | Q | ⊢ G", "output": "Q ⇒ H | H ⇒ F | F ⇒ T | T ⇒ G | Q | ⊢ G", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 6, "reasoning_steps": 4}, {"input": "Q ⇒ T | T ⇒ F | F ⇒ G | G ⇒ H | Q | ⊢ H", "output": "Q ⇒ T | T ⇒ F | F ⇒ G | G ⇒ H | Q | ⊢ H", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 6, "reasoning_steps": 4}, {"input": "T ⇒ Q | Q ⇒ P | T | ⊢ P", "output": "T ⇒ Q | Q ⇒ P | T | ⊢ P", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 4, "reasoning_steps": 2}, {"input": "T ⇒ H | H ⇒ R | T | ⊢ R", "output": "T ⇒ H | H ⇒ R | T | ⊢ R", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 4, "reasoning_steps": 2}, {"input": "P ⇒ H | H ⇒ Q | P | ⊢ Q", "output": "P ⇒ H | H ⇒ Q | P | ⊢ Q", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 4, "reasoning_steps": 2}, {"input": "F ⇒ G | G ⇒ T | T ⇒ P | P ⇒ R | F | ⊢ R", "output": "F ⇒ G | G ⇒ T | T ⇒ P | P ⇒ R | F | ⊢ R", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 6, "reasoning_steps": 4}, {"input": "S ⇒ P | P ⇒ H | H ⇒ G | G ⇒ T | S | ⊢ T", "output": "S ⇒ P | P ⇒ H | H ⇒ G | G ⇒ T | S | ⊢ T", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 6, "reasoning_steps": 4}, {"input": "Even(y) ⇒ DivisibleBy2(y) | DivisibleBy2(y) ⇒ Integer(y) | Integer(y) ⇒ Real(y) | Even(10) | ⊢ Real(10)", "output": "Even(y) ⇒ DivisibleBy2(y) | DivisibleBy2(y) ⇒ Integer(y) | Integer(y) ⇒ Real(y) | Even(10) | ⊢ Real(10)", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 5, "reasoning_steps": 3}, {"input": "Even(y) ⇒ DivisibleBy2(y) | DivisibleBy2(y) ⇒ Integer(y) | Integer(y) ⇒ Real(y) | Even(10) | ⊢ Real(10)", "output": "Even(y) ⇒ DivisibleBy2(y) | DivisibleBy2(y) ⇒ Integer(y) | Integer(y) ⇒ Real(y) | Even(10) | ⊢ Real(10)", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 5, "reasoning_steps": 3}, {"input": "Even(x) ⇒ DivisibleBy2(x) | DivisibleBy2(x) ⇒ Integer(x) | Integer(x) ⇒ Real(x) | Even(12) | ⊢ Real(12)", "output": "Even(x) ⇒ DivisibleBy2(x) | DivisibleBy2(x) ⇒ Integer(x) | Integer(x) ⇒ Real(x) | Even(12) | ⊢ Real(12)", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 5, "reasoning_steps": 3}, {"input": "S ⇒ G | G ⇒ Q | S | ⊢ Q", "output": "S ⇒ G | G ⇒ Q | S | ⊢ Q", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 4, "reasoning_steps": 2}, {"input": "H ⇒ P | P ⇒ Q | Q ⇒ F | F ⇒ R | H | ⊢ R", "output": "H ⇒ P | P ⇒ Q | Q ⇒ F | F ⇒ R | H | ⊢ R", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 6, "reasoning_steps": 4}, {"input": "R ⇒ F | F ⇒ P | P ⇒ H | H ⇒ T | R | ⊢ T", "output": "R ⇒ F | F ⇒ P | P ⇒ H | H ⇒ T | R | ⊢ T", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 6, "reasoning_steps": 4}, {"input": "P ⇒ R | R ⇒ T | T ⇒ Q | Q ⇒ F | P | ⊢ F", "output": "P ⇒ R | R ⇒ T | T ⇒ Q | Q ⇒ F | P | ⊢ F", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 6, "reasoning_steps": 4}, {"input": "F ⇒ S | S ⇒ G | F | ⊢ G", "output": "F ⇒ S | S ⇒ G | F | ⊢ G", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 4, "reasoning_steps": 2}, {"input": "R ⇒ T | T ⇒ S | R | ⊢ S", "output": "R ⇒ T | T ⇒ S | R | ⊢ S", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 4, "reasoning_steps": 2}, {"input": "Q ⇒ P | P ⇒ G | G ⇒ F | F ⇒ T | Q | ⊢ T", "output": "Q ⇒ P | P ⇒ G | G ⇒ F | F ⇒ T | Q | ⊢ T", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 6, "reasoning_steps": 4}, {"input": "Even(x) ⇒ DivisibleBy2(x) | DivisibleBy2(x) ⇒ Integer(x) | Integer(x) ⇒ Real(x) | Even(12) | ⊢ Real(12)", "output": "Even(x) ⇒ DivisibleBy2(x) | DivisibleBy2(x) ⇒ Integer(x) | Integer(x) ⇒ Real(x) | Even(12) | ⊢ Real(12)", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 5, "reasoning_steps": 3}, {"input": "Q ⇒ R | R ⇒ P | Q | ⊢ P", "output": "Q ⇒ R | R ⇒ P | Q | ⊢ P", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 4, "reasoning_steps": 2}, {"input": "Even(y) ⇒ DivisibleBy2(y) | DivisibleBy2(y) ⇒ Integer(y) | Integer(y) ⇒ Real(y) | Even(12) | ⊢ Real(12)", "output": "Even(y) ⇒ DivisibleBy2(y) | DivisibleBy2(y) ⇒ Integer(y) | Integer(y) ⇒ Real(y) | Even(12) | ⊢ Real(12)", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 5, "reasoning_steps": 3}, {"input": "Q ⇒ T | T ⇒ P | P ⇒ S | S ⇒ G | Q | ⊢ G", "output": "Q ⇒ T | T ⇒ P | P ⇒ S | S ⇒ G | Q | ⊢ G", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 6, "reasoning_steps": 4}, {"input": "R ⇒ G | G ⇒ H | R | ⊢ H", "output": "R ⇒ G | G ⇒ H | R | ⊢ H", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 4, "reasoning_steps": 2}, {"input": "S ⇒ G | G ⇒ H | S | ⊢ H", "output": "S ⇒ G | G ⇒ H | S | ⊢ H", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 4, "reasoning_steps": 2}, {"input": "S ⇒ P | P ⇒ T | T ⇒ R | R ⇒ H | S | ⊢ H", "output": "S ⇒ P | P ⇒ T | T ⇒ R | R ⇒ H | S | ⊢ H", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 6, "reasoning_steps": 4}, {"input": "G ⇒ F | F ⇒ R | R ⇒ T | T ⇒ Q | G | ⊢ Q", "output": "G ⇒ F | F ⇒ R | R ⇒ T | T ⇒ Q | G | ⊢ Q", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 6, "reasoning_steps": 4}, {"input": "T ⇒ G | G ⇒ F | T | ⊢ F", "output": "T ⇒ G | G ⇒ F | T | ⊢ F", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 4, "reasoning_steps": 2}, {"input": "T ⇒ H | H ⇒ G | T | ⊢ G", "output": "T ⇒ H | H ⇒ G | T | ⊢ G", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 4, "reasoning_steps": 2}, {"input": "T ⇒ S | S ⇒ Q | T | ⊢ Q", "output": "T ⇒ S | S ⇒ Q | T | ⊢ Q", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 4, "reasoning_steps": 2}, {"input": "Even(y) ⇒ DivisibleBy2(y) | DivisibleBy2(y) ⇒ Integer(y) | Integer(y) ⇒ Real(y) | Even(6) | ⊢ Real(6)", "output": "Even(y) ⇒ DivisibleBy2(y) | DivisibleBy2(y) ⇒ Integer(y) | Integer(y) ⇒ Real(y) | Even(6) | ⊢ Real(6)", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 5, "reasoning_steps": 3}, {"input": "F ⇒ H | H ⇒ S | F | ⊢ S", "output": "F ⇒ H | H ⇒ S | F | ⊢ S", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 4, "reasoning_steps": 2}, {"input": "Q ⇒ G | G ⇒ R | R ⇒ H | H ⇒ F | Q | ⊢ F", "output": "Q ⇒ G | G ⇒ R | R ⇒ H | H ⇒ F | Q | ⊢ F", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 6, "reasoning_steps": 4}, {"input": "P ⇒ H | H ⇒ T | P | ⊢ T", "output": "P ⇒ H | H ⇒ T | P | ⊢ T", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 4, "reasoning_steps": 2}, {"input": "F ⇒ R | R ⇒ Q | Q ⇒ H | H ⇒ P | F | ⊢ P", "output": "F ⇒ R | R ⇒ Q | Q ⇒ H | H ⇒ P | F | ⊢ P", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 6, "reasoning_steps": 4}, {"input": "H ⇒ Q | Q ⇒ S | S ⇒ T | T ⇒ P | H | ⊢ P", "output": "H ⇒ Q | Q ⇒ S | S ⇒ T | T ⇒ P | H | ⊢ P", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 6, "reasoning_steps": 4}, {"input": "H ⇒ Q | Q ⇒ G | G ⇒ F | F ⇒ T | H | ⊢ T", "output": "H ⇒ Q | Q ⇒ G | G ⇒ F | F ⇒ T | H | ⊢ T", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 6, "reasoning_steps": 4}, {"input": "Even(z) ⇒ DivisibleBy2(z) | DivisibleBy2(z) ⇒ Integer(z) | Integer(z) ⇒ Real(z) | Even(4) | ⊢ Real(4)", "output": "Even(z) ⇒ DivisibleBy2(z) | DivisibleBy2(z) ⇒ Integer(z) | Integer(z) ⇒ Real(z) | Even(4) | ⊢ Real(4)", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 5, "reasoning_steps": 3}, {"input": "F ⇒ H | H ⇒ G | G ⇒ R | R ⇒ P | F | ⊢ P", "output": "F ⇒ H | H ⇒ G | G ⇒ R | R ⇒ P | F | ⊢ P", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 6, "reasoning_steps": 4}, {"input": "Even(y) ⇒ DivisibleBy2(y) | DivisibleBy2(y) ⇒ Integer(y) | Integer(y) ⇒ Real(y) | Even(10) | ⊢ Real(10)", "output": "Even(y) ⇒ DivisibleBy2(y) | DivisibleBy2(y) ⇒ Integer(y) | Integer(y) ⇒ Real(y) | Even(10) | ⊢ Real(10)", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 5, "reasoning_steps": 3}, {"input": "S ⇒ G | G ⇒ H | H ⇒ Q | Q ⇒ P | S | ⊢ P", "output": "S ⇒ G | G ⇒ H | H ⇒ Q | Q ⇒ P | S | ⊢ P", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 6, "reasoning_steps": 4}, {"input": "Even(z) ⇒ DivisibleBy2(z) | DivisibleBy2(z) ⇒ Integer(z) | Integer(z) ⇒ Real(z) | Even(8) | ⊢ Real(8)", "output": "Even(z) ⇒ DivisibleBy2(z) | DivisibleBy2(z) ⇒ Integer(z) | Integer(z) ⇒ Real(z) | Even(8) | ⊢ Real(8)", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 5, "reasoning_steps": 3}, {"input": "P ⇒ H | H ⇒ F | F ⇒ R | R ⇒ G | P | ⊢ G", "output": "P ⇒ H | H ⇒ F | F ⇒ R | R ⇒ G | P | ⊢ G", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 6, "reasoning_steps": 4}, {"input": "Even(y) ⇒ DivisibleBy2(y) | DivisibleBy2(y) ⇒ Integer(y) | Integer(y) ⇒ Real(y) | Even(12) | ⊢ Real(12)", "output": "Even(y) ⇒ DivisibleBy2(y) | DivisibleBy2(y) ⇒ Integer(y) | Integer(y) ⇒ Real(y) | Even(12) | ⊢ Real(12)", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 5, "reasoning_steps": 3}, {"input": "Even(x) ⇒ DivisibleBy2(x) | DivisibleBy2(x) ⇒ Integer(x) | Integer(x) ⇒ Real(x) | Even(8) | ⊢ Real(8)", "output": "Even(x) ⇒ DivisibleBy2(x) | DivisibleBy2(x) ⇒ Integer(x) | Integer(x) ⇒ Real(x) | Even(8) | ⊢ Real(8)", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 5, "reasoning_steps": 3}, {"input": "T ⇒ R | R ⇒ G | G ⇒ Q | Q ⇒ F | T | ⊢ F", "output": "T ⇒ R | R ⇒ G | G ⇒ Q | Q ⇒ F | T | ⊢ F", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 6, "reasoning_steps": 4}, {"input": "Q ⇒ T | T ⇒ G | Q | ⊢ G", "output": "Q ⇒ T | T ⇒ G | Q | ⊢ G", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 4, "reasoning_steps": 2}, {"input": "Even(x) ⇒ DivisibleBy2(x) | DivisibleBy2(x) ⇒ Integer(x) | Integer(x) ⇒ Real(x) | Even(6) | ⊢ Real(6)", "output": "Even(x) ⇒ DivisibleBy2(x) | DivisibleBy2(x) ⇒ Integer(x) | Integer(x) ⇒ Real(x) | Even(6) | ⊢ Real(6)", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 5, "reasoning_steps": 3}, {"input": "S ⇒ H | H ⇒ F | F ⇒ G | G ⇒ Q | S | ⊢ Q", "output": "S ⇒ H | H ⇒ F | F ⇒ G | G ⇒ Q | S | ⊢ Q", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 6, "reasoning_steps": 4}, {"input": "S ⇒ T | T ⇒ P | S | ⊢ P", "output": "S ⇒ T | T ⇒ P | S | ⊢ P", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 4, "reasoning_steps": 2}, {"input": "Even(x) ⇒ DivisibleBy2(x) | DivisibleBy2(x) ⇒ Integer(x) | Integer(x) ⇒ Real(x) | Even(4) | ⊢ Real(4)", "output": "Even(x) ⇒ DivisibleBy2(x) | DivisibleBy2(x) ⇒ Integer(x) | Integer(x) ⇒ Real(x) | Even(4) | ⊢ Real(4)", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 5, "reasoning_steps": 3}, {"input": "Even(x) ⇒ DivisibleBy2(x) | DivisibleBy2(x) ⇒ Integer(x) | Integer(x) ⇒ Real(x) | Even(4) | ⊢ Real(4)", "output": "Even(x) ⇒ DivisibleBy2(x) | DivisibleBy2(x) ⇒ Integer(x) | Integer(x) ⇒ Real(x) | Even(4) | ⊢ Real(4)", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 5, "reasoning_steps": 3}, {"input": "R ⇒ Q | Q ⇒ T | R | ⊢ T", "output": "R ⇒ Q | Q ⇒ T | R | ⊢ T", "fidelity": 1.0, "ast_type": "MultiHopReasoning", "category": "multi_hop_reasoning", "generated": true, "complexity": 4, "reasoning_steps": 2}, {"input": "Explain what ∃x ∈ ℝ: P(x) means in English", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "This is not a symbolic expression", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Translate ∀x ∃y: R(x,y) to plain text", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "How do you read ∀x ∈ ℕ: P(x)?", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Convert this to natural language: ∀x: P(x)", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Define the meaning of ⇒ in logic", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Explain the logical operator ∨", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "How do you read ∀x ∈ ℕ: P(x)?", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "This is not a symbolic expression", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "What does the symbol ∧ represent?", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Convert P ∧ Q ⇒ R to English", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Translate ∀x ∃y: R(x,y) to plain text", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Explain the logical operator ∨", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Define the meaning of ⇒ in logic", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Convert P ∧ Q ⇒ R to English", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "This is not a symbolic expression", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Convert this to natural language: ∀x: P(x)", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Define the meaning of ⇒ in logic", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Define the meaning of ⇒ in logic", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Define the meaning of ⇒ in logic", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Translate ∀x ∃y: R(x,y) to plain text", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Explain what ∃x ∈ ℝ: P(x) means in English", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "How do you read ∀x ∈ ℕ: P(x)?", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Translate ∀x ∃y: R(x,y) to plain text", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Translate ∀x ∃y: R(x,y) to plain text", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Translate ∀x ∃y: R(x,y) to plain text", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Convert this to natural language: ∀x: P(x)", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "What is the difference between ∀ and ∃?", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Convert this to natural language: ∀x: P(x)", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "What does the symbol ∧ represent?", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Define the meaning of ⇒ in logic", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Convert P ∧ Q ⇒ R to English", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Translate ∀x ∃y: R(x,y) to plain text", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Translate ∀x ∃y: R(x,y) to plain text", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Convert this to natural language: ∀x: P(x)", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Define the meaning of ⇒ in logic", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Translate ∀x ∃y: R(x,y) to plain text", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Convert this to natural language: ∀x: P(x)", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Translate ∀x ∃y: R(x,y) to plain text", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Convert P ∧ Q ⇒ R to English", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "How do you read ∀x ∈ ℕ: P(x)?", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "What does the symbol ∧ represent?", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Explain what ∃x ∈ ℝ: P(x) means in English", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Explain what ∃x ∈ ℝ: P(x) means in English", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Translate ∀x ∃y: R(x,y) to plain text", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Convert P ∧ Q ⇒ R to English", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Translate ∀x ∃y: R(x,y) to plain text", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Define the meaning of ⇒ in logic", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Explain what ∃x ∈ ℝ: P(x) means in English", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Convert this to natural language: ∀x: P(x)", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "How do you read ∀x ∈ ℕ: P(x)?", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Convert P ∧ Q ⇒ R to English", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Explain what ∃x ∈ ℝ: P(x) means in English", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "What does the symbol ∧ represent?", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Explain what ∃x ∈ ℝ: P(x) means in English", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "This is not a symbolic expression", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Convert P ∧ Q ⇒ R to English", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Explain the logical operator ∨", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Define the meaning of ⇒ in logic", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "This is not a symbolic expression", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Explain what ∃x ∈ ℝ: P(x) means in English", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "What does the symbol ∧ represent?", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Explain what ∃x ∈ ℝ: P(x) means in English", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Explain what ∃x ∈ ℝ: P(x) means in English", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Explain what ∃x ∈ ℝ: P(x) means in English", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Explain the logical operator ∨", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Explain what ∃x ∈ ℝ: P(x) means in English", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Explain the logical operator ∨", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Explain the logical operator ∨", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "This is not a symbolic expression", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Convert P ∧ Q ⇒ R to English", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Define the meaning of ⇒ in logic", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Convert this to natural language: ∀x: P(x)", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Convert P ∧ Q ⇒ R to English", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "What does the symbol ∧ represent?", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "What does the symbol ∧ represent?", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "What does the symbol ∧ represent?", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Explain the logical operator ∨", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Define the meaning of ⇒ in logic", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Translate ∀x ∃y: R(x,y) to plain text", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Convert this to natural language: ∀x: P(x)", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Explain what ∃x ∈ ℝ: P(x) means in English", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "What does the symbol ∧ represent?", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Convert this to natural language: ∀x: P(x)", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Define the meaning of ⇒ in logic", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "This is not a symbolic expression", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "What does the symbol ∧ represent?", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Explain the logical operator ∨", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Define the meaning of ⇒ in logic", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "This is not a symbolic expression", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "What does the symbol ∧ represent?", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "This is not a symbolic expression", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Translate ∀x ∃y: R(x,y) to plain text", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "This is not a symbolic expression", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Define the meaning of ⇒ in logic", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Explain what ∃x ∈ ℝ: P(x) means in English", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Define the meaning of ⇒ in logic", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Translate ∀x ∃y: R(x,y) to plain text", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "What does the symbol ∧ represent?", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "What does the symbol ∧ represent?", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Explain the logical operator ∨", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Explain what ∃x ∈ ℝ: P(x) means in English", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Explain what ∃x ∈ ℝ: P(x) means in English", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "This is not a symbolic expression", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "What is the difference between ∀ and ∃?", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "What does the symbol ∧ represent?", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Convert P ∧ Q ⇒ R to English", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "This is not a symbolic expression", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "This is not a symbolic expression", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Define the meaning of ⇒ in logic", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Translate ∀x ∃y: R(x,y) to plain text", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "What does the symbol ∧ represent?", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "How do you read ∀x ∈ ℕ: P(x)?", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Explain what ∃x ∈ ℝ: P(x) means in English", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Translate ∀x ∃y: R(x,y) to plain text", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "What does the symbol ∧ represent?", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "What does the symbol ∧ represent?", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "This is not a symbolic expression", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Convert P ∧ Q ⇒ R to English", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "What does the symbol ∧ represent?", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Convert this to natural language: ∀x: P(x)", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Explain the logical operator ∨", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Translate ∀x ∃y: R(x,y) to plain text", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Convert this to natural language: ∀x: P(x)", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Define the meaning of ⇒ in logic", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Explain the logical operator ∨", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Explain what ∃x ∈ ℝ: P(x) means in English", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Convert P ∧ Q ⇒ R to English", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Define the meaning of ⇒ in logic", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Convert this to natural language: ∀x: P(x)", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Explain what ∃x ∈ ℝ: P(x) means in English", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "This is not a symbolic expression", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Convert this to natural language: ∀x: P(x)", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Explain what ∃x ∈ ℝ: P(x) means in English", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Convert this to natural language: ∀x: P(x)", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Convert this to natural language: ∀x: P(x)", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Explain the logical operator ∨", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Explain the logical operator ∨", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Convert P ∧ Q ⇒ R to English", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "What does the symbol ∧ represent?", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "What does the symbol ∧ represent?", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Explain what ∃x ∈ ℝ: P(x) means in English", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Convert this to natural language: ∀x: P(x)", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "This is not a symbolic expression", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Convert P ∧ Q ⇒ R to English", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Convert this to natural language: ∀x: P(x)", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Convert P ∧ Q ⇒ R to English", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Define the meaning of ⇒ in logic", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "This is not a symbolic expression", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}, {"input": "Convert P ∧ Q ⇒ R to English", "output": "<NO_NG>", "fidelity": 1.0, "ast_type": "ContrastiveExample", "category": "contrastive", "contrastive": true, "generated": true}]}