{"id": "logiqa_001", "context": "All birds can fly. Penguins are birds. However, penguins cannot fly.", "question": "What can we conclude from this information?", "A": "The initial statement is incorrect", "B": "Penguins are not really birds", "C": "There is a contradiction in the given statements", "D": "Some birds cannot fly despite the general rule", "answer": "C", "category": "logical_reasoning", "difficulty": "medium"}
{"id": "logiqa_002", "context": "If it rains, the ground gets wet. The ground is wet.", "question": "What can we conclude?", "A": "It rained", "B": "It might have rained, but we cannot be certain", "C": "It did not rain", "D": "The ground is always wet", "answer": "B", "category": "logical_reasoning", "difficulty": "medium"}
{"id": "logiqa_003", "context": "All students who study hard pass the exam. <PERSON> passed the exam.", "question": "What can we conclude about <PERSON>?", "A": "<PERSON> studied hard", "B": "<PERSON> might have studied hard, but we cannot be certain", "C": "<PERSON> did not study hard", "D": "<PERSON> always passes exams", "answer": "B", "category": "logical_reasoning", "difficulty": "hard"}
{"id": "logiqa_004", "context": "All birds can fly. Penguins are birds. However, penguins cannot fly.", "question": "What can we conclude from this information?", "A": "The initial statement is incorrect", "B": "Penguins are not really birds", "C": "There is a contradiction in the given statements", "D": "Some birds cannot fly despite the general rule", "answer": "C", "category": "logical_reasoning", "difficulty": "medium"}
{"id": "logiqa_005", "context": "If it rains, the ground gets wet. The ground is wet.", "question": "What can we conclude?", "A": "It rained", "B": "It might have rained, but we cannot be certain", "C": "It did not rain", "D": "The ground is always wet", "answer": "B", "category": "logical_reasoning", "difficulty": "medium"}
{"id": "logiqa_006", "context": "All students who study hard pass the exam. John passed the exam.", "question": "What can we conclude about John?", "A": "John studied hard", "B": "John might have studied hard, but we cannot be certain", "C": "John did not study hard", "D": "John always passes exams", "answer": "B", "category": "logical_reasoning", "difficulty": "hard"}
{"id": "logiqa_007", "context": "All birds can fly. Penguins are birds. However, penguins cannot fly.", "question": "What can we conclude from this information?", "A": "The initial statement is incorrect", "B": "Penguins are not really birds", "C": "There is a contradiction in the given statements", "D": "Some birds cannot fly despite the general rule", "answer": "C", "category": "logical_reasoning", "difficulty": "medium"}
{"id": "logiqa_008", "context": "If it rains, the ground gets wet. The ground is wet.", "question": "What can we conclude?", "A": "It rained", "B": "It might have rained, but we cannot be certain", "C": "It did not rain", "D": "The ground is always wet", "answer": "B", "category": "logical_reasoning", "difficulty": "medium"}
{"id": "logiqa_009", "context": "All students who study hard pass the exam. John passed the exam.", "question": "What can we conclude about John?", "A": "John studied hard", "B": "John might have studied hard, but we cannot be certain", "C": "John did not study hard", "D": "John always passes exams", "answer": "B", "category": "logical_reasoning", "difficulty": "hard"}
{"id": "logiqa_010", "context": "All birds can fly. Penguins are birds. However, penguins cannot fly.", "question": "What can we conclude from this information?", "A": "The initial statement is incorrect", "B": "Penguins are not really birds", "C": "There is a contradiction in the given statements", "D": "Some birds cannot fly despite the general rule", "answer": "C", "category": "logical_reasoning", "difficulty": "medium"}
{"id": "logiqa_011", "context": "If it rains, the ground gets wet. The ground is wet.", "question": "What can we conclude?", "A": "It rained", "B": "It might have rained, but we cannot be certain", "C": "It did not rain", "D": "The ground is always wet", "answer": "B", "category": "logical_reasoning", "difficulty": "medium"}
{"id": "logiqa_012", "context": "All students who study hard pass the exam. John passed the exam.", "question": "What can we conclude about John?", "A": "John studied hard", "B": "John might have studied hard, but we cannot be certain", "C": "John did not study hard", "D": "John always passes exams", "answer": "B", "category": "logical_reasoning", "difficulty": "hard"}
{"id": "logiqa_013", "context": "All birds can fly. Penguins are birds. However, penguins cannot fly.", "question": "What can we conclude from this information?", "A": "The initial statement is incorrect", "B": "Penguins are not really birds", "C": "There is a contradiction in the given statements", "D": "Some birds cannot fly despite the general rule", "answer": "C", "category": "logical_reasoning", "difficulty": "medium"}
{"id": "logiqa_014", "context": "If it rains, the ground gets wet. The ground is wet.", "question": "What can we conclude?", "A": "It rained", "B": "It might have rained, but we cannot be certain", "C": "It did not rain", "D": "The ground is always wet", "answer": "B", "category": "logical_reasoning", "difficulty": "medium"}
{"id": "logiqa_015", "context": "All students who study hard pass the exam. John passed the exam.", "question": "What can we conclude about John?", "A": "John studied hard", "B": "John might have studied hard, but we cannot be certain", "C": "John did not study hard", "D": "John always passes exams", "answer": "B", "category": "logical_reasoning", "difficulty": "hard"}
{"id": "logiqa_016", "context": "All birds can fly. Penguins are birds. However, penguins cannot fly.", "question": "What can we conclude from this information?", "A": "The initial statement is incorrect", "B": "Penguins are not really birds", "C": "There is a contradiction in the given statements", "D": "Some birds cannot fly despite the general rule", "answer": "C", "category": "logical_reasoning", "difficulty": "medium"}
{"id": "logiqa_017", "context": "If it rains, the ground gets wet. The ground is wet.", "question": "What can we conclude?", "A": "It rained", "B": "It might have rained, but we cannot be certain", "C": "It did not rain", "D": "The ground is always wet", "answer": "B", "category": "logical_reasoning", "difficulty": "medium"}
{"id": "logiqa_018", "context": "All students who study hard pass the exam. John passed the exam.", "question": "What can we conclude about John?", "A": "John studied hard", "B": "John might have studied hard, but we cannot be certain", "C": "John did not study hard", "D": "John always passes exams", "answer": "B", "category": "logical_reasoning", "difficulty": "hard"}
{"id": "logiqa_019", "context": "All birds can fly. Penguins are birds. However, penguins cannot fly.", "question": "What can we conclude from this information?", "A": "The initial statement is incorrect", "B": "Penguins are not really birds", "C": "There is a contradiction in the given statements", "D": "Some birds cannot fly despite the general rule", "answer": "C", "category": "logical_reasoning", "difficulty": "medium"}
{"id": "logiqa_020", "context": "If it rains, the ground gets wet. The ground is wet.", "question": "What can we conclude?", "A": "It rained", "B": "It might have rained, but we cannot be certain", "C": "It did not rain", "D": "The ground is always wet", "answer": "B", "category": "logical_reasoning", "difficulty": "medium"}
{"id": "logiqa_021", "context": "All students who study hard pass the exam. John passed the exam.", "question": "What can we conclude about John?", "A": "John studied hard", "B": "John might have studied hard, but we cannot be certain", "C": "John did not study hard", "D": "John always passes exams", "answer": "B", "category": "logical_reasoning", "difficulty": "hard"}
{"id": "logiqa_022", "context": "All birds can fly. Penguins are birds. However, penguins cannot fly.", "question": "What can we conclude from this information?", "A": "The initial statement is incorrect", "B": "Penguins are not really birds", "C": "There is a contradiction in the given statements", "D": "Some birds cannot fly despite the general rule", "answer": "C", "category": "logical_reasoning", "difficulty": "medium"}
{"id": "logiqa_023", "context": "If it rains, the ground gets wet. The ground is wet.", "question": "What can we conclude?", "A": "It rained", "B": "It might have rained, but we cannot be certain", "C": "It did not rain", "D": "The ground is always wet", "answer": "B", "category": "logical_reasoning", "difficulty": "medium"}
{"id": "logiqa_024", "context": "All students who study hard pass the exam. John passed the exam.", "question": "What can we conclude about John?", "A": "John studied hard", "B": "John might have studied hard, but we cannot be certain", "C": "John did not study hard", "D": "John always passes exams", "answer": "B", "category": "logical_reasoning", "difficulty": "hard"}
{"id": "logiqa_025", "context": "All birds can fly. Penguins are birds. However, penguins cannot fly.", "question": "What can we conclude from this information?", "A": "The initial statement is incorrect", "B": "Penguins are not really birds", "C": "There is a contradiction in the given statements", "D": "Some birds cannot fly despite the general rule", "answer": "C", "category": "logical_reasoning", "difficulty": "medium"}
{"id": "logiqa_026", "context": "If it rains, the ground gets wet. The ground is wet.", "question": "What can we conclude?", "A": "It rained", "B": "It might have rained, but we cannot be certain", "C": "It did not rain", "D": "The ground is always wet", "answer": "B", "category": "logical_reasoning", "difficulty": "medium"}
{"id": "logiqa_027", "context": "All students who study hard pass the exam. John passed the exam.", "question": "What can we conclude about John?", "A": "John studied hard", "B": "John might have studied hard, but we cannot be certain", "C": "John did not study hard", "D": "John always passes exams", "answer": "B", "category": "logical_reasoning", "difficulty": "hard"}
{"id": "logiqa_028", "context": "All birds can fly. Penguins are birds. However, penguins cannot fly.", "question": "What can we conclude from this information?", "A": "The initial statement is incorrect", "B": "Penguins are not really birds", "C": "There is a contradiction in the given statements", "D": "Some birds cannot fly despite the general rule", "answer": "C", "category": "logical_reasoning", "difficulty": "medium"}
{"id": "logiqa_029", "context": "If it rains, the ground gets wet. The ground is wet.", "question": "What can we conclude?", "A": "It rained", "B": "It might have rained, but we cannot be certain", "C": "It did not rain", "D": "The ground is always wet", "answer": "B", "category": "logical_reasoning", "difficulty": "medium"}
{"id": "logiqa_030", "context": "All students who study hard pass the exam. John passed the exam.", "question": "What can we conclude about John?", "A": "John studied hard", "B": "John might have studied hard, but we cannot be certain", "C": "John did not study hard", "D": "John always passes exams", "answer": "B", "category": "logical_reasoning", "difficulty": "hard"}
{"id": "logiqa_031", "context": "All birds can fly. Penguins are birds. However, penguins cannot fly.", "question": "What can we conclude from this information?", "A": "The initial statement is incorrect", "B": "Penguins are not really birds", "C": "There is a contradiction in the given statements", "D": "Some birds cannot fly despite the general rule", "answer": "C", "category": "logical_reasoning", "difficulty": "medium"}
{"id": "logiqa_032", "context": "If it rains, the ground gets wet. The ground is wet.", "question": "What can we conclude?", "A": "It rained", "B": "It might have rained, but we cannot be certain", "C": "It did not rain", "D": "The ground is always wet", "answer": "B", "category": "logical_reasoning", "difficulty": "medium"}
{"id": "logiqa_033", "context": "All students who study hard pass the exam. John passed the exam.", "question": "What can we conclude about John?", "A": "John studied hard", "B": "John might have studied hard, but we cannot be certain", "C": "John did not study hard", "D": "John always passes exams", "answer": "B", "category": "logical_reasoning", "difficulty": "hard"}
{"id": "logiqa_034", "context": "All birds can fly. Penguins are birds. However, penguins cannot fly.", "question": "What can we conclude from this information?", "A": "The initial statement is incorrect", "B": "Penguins are not really birds", "C": "There is a contradiction in the given statements", "D": "Some birds cannot fly despite the general rule", "answer": "C", "category": "logical_reasoning", "difficulty": "medium"}
{"id": "logiqa_035", "context": "If it rains, the ground gets wet. The ground is wet.", "question": "What can we conclude?", "A": "It rained", "B": "It might have rained, but we cannot be certain", "C": "It did not rain", "D": "The ground is always wet", "answer": "B", "category": "logical_reasoning", "difficulty": "medium"}
{"id": "logiqa_036", "context": "All students who study hard pass the exam. John passed the exam.", "question": "What can we conclude about John?", "A": "John studied hard", "B": "John might have studied hard, but we cannot be certain", "C": "John did not study hard", "D": "John always passes exams", "answer": "B", "category": "logical_reasoning", "difficulty": "hard"}
{"id": "logiqa_037", "context": "All birds can fly. Penguins are birds. However, penguins cannot fly.", "question": "What can we conclude from this information?", "A": "The initial statement is incorrect", "B": "Penguins are not really birds", "C": "There is a contradiction in the given statements", "D": "Some birds cannot fly despite the general rule", "answer": "C", "category": "logical_reasoning", "difficulty": "medium"}
{"id": "logiqa_038", "context": "If it rains, the ground gets wet. The ground is wet.", "question": "What can we conclude?", "A": "It rained", "B": "It might have rained, but we cannot be certain", "C": "It did not rain", "D": "The ground is always wet", "answer": "B", "category": "logical_reasoning", "difficulty": "medium"}
{"id": "logiqa_039", "context": "All students who study hard pass the exam. John passed the exam.", "question": "What can we conclude about John?", "A": "John studied hard", "B": "John might have studied hard, but we cannot be certain", "C": "John did not study hard", "D": "John always passes exams", "answer": "B", "category": "logical_reasoning", "difficulty": "hard"}
{"id": "logiqa_040", "context": "All birds can fly. Penguins are birds. However, penguins cannot fly.", "question": "What can we conclude from this information?", "A": "The initial statement is incorrect", "B": "Penguins are not really birds", "C": "There is a contradiction in the given statements", "D": "Some birds cannot fly despite the general rule", "answer": "C", "category": "logical_reasoning", "difficulty": "medium"}
{"id": "logiqa_041", "context": "If it rains, the ground gets wet. The ground is wet.", "question": "What can we conclude?", "A": "It rained", "B": "It might have rained, but we cannot be certain", "C": "It did not rain", "D": "The ground is always wet", "answer": "B", "category": "logical_reasoning", "difficulty": "medium"}
{"id": "logiqa_042", "context": "All students who study hard pass the exam. John passed the exam.", "question": "What can we conclude about John?", "A": "John studied hard", "B": "John might have studied hard, but we cannot be certain", "C": "John did not study hard", "D": "John always passes exams", "answer": "B", "category": "logical_reasoning", "difficulty": "hard"}
{"id": "logiqa_043", "context": "All birds can fly. Penguins are birds. However, penguins cannot fly.", "question": "What can we conclude from this information?", "A": "The initial statement is incorrect", "B": "Penguins are not really birds", "C": "There is a contradiction in the given statements", "D": "Some birds cannot fly despite the general rule", "answer": "C", "category": "logical_reasoning", "difficulty": "medium"}
{"id": "logiqa_044", "context": "If it rains, the ground gets wet. The ground is wet.", "question": "What can we conclude?", "A": "It rained", "B": "It might have rained, but we cannot be certain", "C": "It did not rain", "D": "The ground is always wet", "answer": "B", "category": "logical_reasoning", "difficulty": "medium"}
{"id": "logiqa_045", "context": "All students who study hard pass the exam. John passed the exam.", "question": "What can we conclude about John?", "A": "John studied hard", "B": "John might have studied hard, but we cannot be certain", "C": "John did not study hard", "D": "John always passes exams", "answer": "B", "category": "logical_reasoning", "difficulty": "hard"}
{"id": "logiqa_046", "context": "All birds can fly. Penguins are birds. However, penguins cannot fly.", "question": "What can we conclude from this information?", "A": "The initial statement is incorrect", "B": "Penguins are not really birds", "C": "There is a contradiction in the given statements", "D": "Some birds cannot fly despite the general rule", "answer": "C", "category": "logical_reasoning", "difficulty": "medium"}
{"id": "logiqa_047", "context": "If it rains, the ground gets wet. The ground is wet.", "question": "What can we conclude?", "A": "It rained", "B": "It might have rained, but we cannot be certain", "C": "It did not rain", "D": "The ground is always wet", "answer": "B", "category": "logical_reasoning", "difficulty": "medium"}
{"id": "logiqa_048", "context": "All students who study hard pass the exam. John passed the exam.", "question": "What can we conclude about John?", "A": "John studied hard", "B": "John might have studied hard, but we cannot be certain", "C": "John did not study hard", "D": "John always passes exams", "answer": "B", "category": "logical_reasoning", "difficulty": "hard"}
{"id": "logiqa_049", "context": "All birds can fly. Penguins are birds. However, penguins cannot fly.", "question": "What can we conclude from this information?", "A": "The initial statement is incorrect", "B": "Penguins are not really birds", "C": "There is a contradiction in the given statements", "D": "Some birds cannot fly despite the general rule", "answer": "C", "category": "logical_reasoning", "difficulty": "medium"}
{"id": "logiqa_050", "context": "If it rains, the ground gets wet. The ground is wet.", "question": "What can we conclude?", "A": "It rained", "B": "It might have rained, but we cannot be certain", "C": "It did not rain", "D": "The ground is always wet", "answer": "B", "category": "logical_reasoning", "difficulty": "medium"}
